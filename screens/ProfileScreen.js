// screens/ProfileScreen.js
import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Image,
  ScrollView,
  Alert,
  ActivityIndicator,
  Platform,
  KeyboardAvoidingView,
  Keyboard,
  TouchableWithoutFeedback,
  Modal,
  Animated
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import AsyncStorage from '@react-native-async-storage/async-storage';
// Removed SVG import - using native components instead

// Predefined passion options
const PASSION_OPTIONS = [
  'Travel', 'Music', 'Movies', 'Reading', 'Cooking', 'Fitness', 'Photography', 'Art',
  'Dancing', 'Gaming', 'Sports', 'Nature', 'Technology', 'Fashion', 'Food', 'Coffee',
  'Wine', 'Hiking', 'Beach', 'Yoga', 'Meditation', 'Writing', 'Learning', 'Animals',
  'Cars', 'Motorcycles', 'Cycling', 'Running', 'Swimming', 'Skiing', 'Surfing', 'Climbing',
  'Comedy', 'Theater', 'Concerts', 'Festivals', 'Volunteering', 'Gardening', 'DIY',
  'Shopping', 'Nightlife', 'Adventure', 'Culture', 'History', 'Science', 'Politics'
];

// Hand gesture components using native React Native elements
const HandGesture = ({ fingers, size = 120, color = '#4e9af1' }) => {
  const getFingerEmoji = (count) => {
    const fingerEmojis = {
      1: '☝️',
      2: '✌️',
      3: '🤟',
      4: '🖖',
      5: '🖐️'
    };
    return fingerEmojis[count] || '☝️';
  };

  return (
    <View style={{
      alignItems: 'center',
      justifyContent: 'center',
      width: size,
      height: size,
      backgroundColor: 'rgba(78, 154, 241, 0.1)',
      borderRadius: size / 2,
      borderWidth: 3,
      borderColor: color,
      borderStyle: 'dashed'
    }}>
      <Text style={{
        fontSize: size * 0.6,
        textAlign: 'center',
        lineHeight: size * 0.7
      }}>
        {getFingerEmoji(fingers)}
      </Text>
    </View>
  );
};

// Enhanced instruction display with face and bigger emoji
const InstructionDisplay = ({ fingers, size = 100 }) => {
  const getFingerEmoji = (count) => {
    const fingerEmojis = {
      1: '☝️',
      2: '✌️',
      3: '🤟',
      4: '🖖',
      5: '🖐️'
    };
    return fingerEmojis[count] || '☝️';
  };

  return (
    <View style={{ alignItems: 'center', justifyContent: 'center' }}>
      {/* Instruction text */}
      <Text style={{ fontSize: 18, color: '#666', textAlign: 'center', marginBottom: 30 }}>
        Show your face and hold up:
      </Text>

      {/* Hand gesture and face side by side */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 20,
        gap: 30
      }}>
        {/* Big hand gesture - no border */}
        <View style={{
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <Text style={{
            fontSize: size * 0.7,
            textAlign: 'center'
          }}>
            {getFingerEmoji(fingers)}
          </Text>
        </View>

        {/* Face on the right */}
        <View style={{
          alignItems: 'center'
        }}>
          <Text style={{ fontSize: 100 }}>😊</Text>
        </View>
      </View>

      {/* Instruction text */}
      <Text style={{
        fontSize: 24,
        fontWeight: '600',
        color: '#4e9af1',
        textAlign: 'center',
        marginBottom: 10
      }}>
        {fingers} finger{fingers > 1 ? 's' : ''}
      </Text>

      <Text style={{
        fontSize: 16,
        color: '#666',
        textAlign: 'center'
      }}>
        Make sure both your face and fingers are clearly visible
      </Text>
    </View>
  );
};

// Animated verification instruction component
const VerificationAnimation = ({ requiredFingers, style }) => {
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Fade in animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();

    // Pulse animation
    const pulse = () => {
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ]).start(() => pulse());
    };
    pulse();
  }, []);

  return (
    <Animated.View
      style={[
        style,
        {
          opacity: fadeAnim,
          transform: [{ scale: pulseAnim }],
          alignItems: 'center',
          justifyContent: 'center',
        }
      ]}
    >
      <HandGesture fingers={requiredFingers} size={150} color="#4e9af1" />

      {/* Camera frame overlay with corner indicators */}
      <View style={{
        position: 'absolute',
        width: 200,
        height: 200,
        borderWidth: 2,
        borderColor: '#ff4444',
        borderRadius: 20,
        backgroundColor: 'transparent',
      }}>
        {/* Corner brackets */}
        <View style={{ position: 'absolute', top: -2, left: -2, width: 30, height: 30, borderTopWidth: 4, borderLeftWidth: 4, borderColor: '#ff4444', borderTopLeftRadius: 20 }} />
        <View style={{ position: 'absolute', top: -2, right: -2, width: 30, height: 30, borderTopWidth: 4, borderRightWidth: 4, borderColor: '#ff4444', borderTopRightRadius: 20 }} />
        <View style={{ position: 'absolute', bottom: -2, left: -2, width: 30, height: 30, borderBottomWidth: 4, borderLeftWidth: 4, borderColor: '#ff4444', borderBottomLeftRadius: 20 }} />
        <View style={{ position: 'absolute', bottom: -2, right: -2, width: 30, height: 30, borderBottomWidth: 4, borderRightWidth: 4, borderColor: '#ff4444', borderBottomRightRadius: 20 }} />
      </View>
    </Animated.View>
  );
};

const ProfileScreen = ({ navigation, route, user, serverAddress, onUpdateProfile, isInitialSetup = false }) => {
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [age, setAge] = useState('');
  const [showAgeModal, setShowAgeModal] = useState(false);
  const [description, setDescription] = useState('');
  const [passions, setPassions] = useState('');
  const [selectedPassions, setSelectedPassions] = useState([]);
  const [images, setImages] = useState(['', '', '', '']);
  const [profileData, setProfileData] = useState(null);
  const [errors, setErrors] = useState({});  // Add state for validation errors

  // Verification-related state
  const [verificationStatus, setVerificationStatus] = useState({
    isVerified: false,
    badgeType: null,
    verifiedAt: null,
    request: null
  });
  const [isLoadingVerification, setIsLoadingVerification] = useState(false);
  const [showVerificationModal, setShowVerificationModal] = useState(false);
  const [verificationPhoto, setVerificationPhoto] = useState(null);
  const [requiredFingers, setRequiredFingers] = useState(null);
  const [showFingerAnimation, setShowFingerAnimation] = useState(false);
  const [showInstructionModal, setShowInstructionModal] = useState(false);
  const [shouldOpenCamera, setShouldOpenCamera] = useState(false);
  const pulseAnim = useRef(new Animated.Value(1)).current;

  // Refs for input fields (for focusing next input)
  const descriptionRef = useRef(null);
  const passionsRef = useRef(null);

  // Add effect to handle back button and navigation
  useEffect(() => {
    if (isInitialSetup) {
      // Set custom header options to prevent going back
      navigation.setOptions({
        headerTitle: 'Complete Your Profile',
        headerLeft: () => null,  // Remove back button
        gestureEnabled: false,   // Disable swipe back gesture
      });
    }
  }, [navigation, isInitialSetup]);

  // Fetch user profile data when screen loads
  useEffect(() => {
    fetchProfileData();
  }, []);

  // Request permissions for image library
  useEffect(() => {
    (async () => {
      if (Platform.OS !== 'web') {
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert('Permission Required', 
            'Sorry, we need camera roll permissions to upload images.');
        }
      }
    })();
  }, []);

  const fetchProfileData = async () => {
    const userId = getValidUserId();
    if (!userId || !serverAddress) return;

    setLoading(true);
    try {
      // Check if profile is cached
      const cachedProfile = await AsyncStorage.getItem(`profile_${userId}`);
      if (cachedProfile) {
        const parsedProfile = JSON.parse(cachedProfile);
        setProfileData(parsedProfile);
        setAge(parsedProfile.age ? parsedProfile.age.toString() : '');
        setDescription(parsedProfile.description || '');
        setPassions(parsedProfile.passions ? parsedProfile.passions.join(', ') : '');
        setSelectedPassions(parsedProfile.passions || []);
        setImages(
          parsedProfile.images && parsedProfile.images.length > 0 
            ? [...parsedProfile.images, ...Array(4 - parsedProfile.images.length).fill('')] 
            : ['', '', '', '']
        );
      }

      // Fetch from server
      const response = await fetch(`http://${serverAddress}/api/profile/${userId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.log(`Error response from server: ${errorText}`);
        if (response.status === 404) {
          // If user not found, it might be a new user without a profile yet
          // This is not necessarily an error
          setLoading(false);
          return;
        }
        throw new Error(errorText || 'Failed to fetch profile');
      }

      const data = await response.json();
      
      setProfileData(data.profile);
      setAge(data.profile.age ? data.profile.age.toString() : '');
      setDescription(data.profile.description || '');
      setPassions(data.profile.passions ? data.profile.passions.join(', ') : '');
      setSelectedPassions(data.profile.passions || []);
      setImages(
        data.profile.images && data.profile.images.length > 0 
          ? [...data.profile.images, ...Array(4 - data.profile.images.length).fill('')] 
          : ['', '', '', '']
      );

      // Cache the profile data
      await AsyncStorage.setItem(`profile_${userId}`, JSON.stringify(data.profile));
    } catch (error) {
      console.error('Error fetching profile:', error);
      // Don't show an alert for 404 errors since this could be a new user
      if (!error.message.includes('not found')) {
        Alert.alert('Error', 'Failed to load profile data. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleImagePick = async (startIndex) => {
    try {
      // Count available empty slots starting from the clicked index
      const emptySlots = [];
      for (let i = startIndex; i < 4; i++) {
        if (images[i] === '') {
          emptySlots.push(i);
        }
      }

      // If no empty slots from this position, just replace the clicked slot
      if (emptySlots.length === 0) {
        emptySlots.push(startIndex);
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsMultipleSelection: true,
        selectionLimit: emptySlots.length, // Limit to available slots
        aspect: [1, 1],
        quality: 0.2,
        base64: true,
        exif: false,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const newImages = [...images];

        // Fill the available slots with selected images
        result.assets.forEach((asset, assetIndex) => {
          if (assetIndex < emptySlots.length) {
            const slotIndex = emptySlots[assetIndex];
            newImages[slotIndex] = asset.base64;
          }
        });

        setImages(newImages);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image. Please try again.');
    }
  };



  const handleRemoveImage = (index) => {
    const newImages = [...images];
    newImages[index] = '';
    setImages(newImages);
  };

  // Handle passion selection with constraints
  const togglePassion = (passion) => {
    setSelectedPassions(prev => {
      if (prev.includes(passion)) {
        // Removing a passion - check minimum constraint
        if (prev.length <= 3) {
          Alert.alert(
            "Minimum Required",
            "Please select at least 3 passions to continue.",
            [{ text: "OK" }]
          );
          return prev; // Don't remove if at minimum
        }
        return prev.filter(p => p !== passion);
      } else {
        // Adding a passion - check maximum constraint
        if (prev.length >= 6) {
          Alert.alert(
            "Maximum Reached",
            "You can select up to 6 passions. Please remove one to add another.",
            [{ text: "OK" }]
          );
          return prev; // Don't add if at maximum
        }
        return [...prev, passion];
      }
    });
  };

  // Handle photo reordering
  const movePhoto = (fromIndex, toIndex) => {
    if (toIndex < 0 || toIndex >= 4 || fromIndex === toIndex) return;

    const newImages = [...images];
    const temp = newImages[fromIndex];
    newImages[fromIndex] = newImages[toIndex];
    newImages[toIndex] = temp;
    setImages(newImages);
  };

  // Debug function to validate user ID before using it
  const getValidUserId = () => {
    if (!user) {
      console.log('User object is null or undefined');
      return null;
    }
    
    if (!user.id) {
      console.log('User ID is missing in the user object', user);
      return null;
    }
    
    // Check if the ID looks like a valid MongoDB ObjectId
    const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(user.id);
    console.log(`User ID: ${user.id}, valid ObjectId format: ${isValidObjectId}`);
    
    return user.id;
  };

  // Add this function to validate the profile
  const validateProfile = () => {
    const newErrors = {};

    if (!description.trim()) {
      newErrors.description = 'Please add a description about yourself';
    }

    if (!age) {
        newErrors.age = 'Age is required';
      } else {
        const ageNum = parseInt(age);
        if (isNaN(ageNum) || ageNum < 18 || ageNum > 120) {
          newErrors.age = 'Please enter a valid age between 18 and 120';
        }
      }

    // Check passion selection constraints
    if (selectedPassions.length < 3) {
      newErrors.passions = `Please select at least 3 passions (${selectedPassions.length}/3 selected)`;
    } else if (selectedPassions.length > 6) {
      newErrors.passions = `Please select no more than 6 passions (${selectedPassions.length}/6 selected)`;
    }

    // Check for minimum of 3 pictures
    if (isInitialSetup) {
      const photoCount = images.filter(img => img !== '').length;
      if (photoCount < 3) {
        newErrors.photos = `Please add at least 3 photos (${photoCount}/3 uploaded)`;
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSaveProfile = async () => {
    // Dismiss keyboard
    Keyboard.dismiss();
    
    const userId = getValidUserId();
    if (!userId || !serverAddress) {
      Alert.alert('Error', 'Invalid user ID or server address. Please try logging in again.');
      return;
    }

    // Validate profile data
    if (!validateProfile()) {
      // If there are validation errors and this is initial setup, show an alert
      if (isInitialSetup) {
        Alert.alert(
          'Incomplete Profile',
          'Please complete your profile before continuing. A description about yourself is required.',
          [{ text: 'OK' }]
        );
      }
      return;
    }

    setSaving(true);
    try {
      // Filter out empty images and ensure proper format
      const filteredImages = images
        .filter(img => img !== '')
        .map(img => {
          // If it's already a data URI, extract just the base64 part
          if (img.startsWith('data:image')) {
            return img.split(',')[1];
          }
          // Return as is if it's just the base64 string
          return img;
        });
      
      // Check if images are too large
      const totalImagesSize = JSON.stringify(filteredImages).length;
      if (totalImagesSize > 5000000) { // ~5MB limit
        Alert.alert(
          'Images Too Large', 
          'Your images are too large. Please try using fewer or smaller images.',
          [{ text: 'OK' }]
        );
        setSaving(false);
        return;
      }
      
      const profileData = {
        age: age ? parseInt(age) : null,
        description,
        passions: selectedPassions,
        images: filteredImages,
      };

      console.log(`Saving profile for user ID: ${userId}`);
      
      const response = await fetch(`http://${serverAddress}/api/profile/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(profileData),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.log(`Error response from server: ${errorText}`);
        throw new Error(errorText || 'Failed to update profile');
      }
      
      const data = await response.json().catch(e => {
        console.log('Error parsing JSON response:', e);
        return { profile: profileData };
      });

      // Cache the updated profile data
      await AsyncStorage.setItem(`profile_${userId}`, JSON.stringify(data.profile || profileData));
      
      // Update app state if callback provided
      if (onUpdateProfile) {
        onUpdateProfile(data.profile || profileData);
      }

      if (isInitialSetup) {
        // Show alert and then navigate to Home screen
        Alert.alert(
          'Profile Completed',
          'Your profile has been set up. You can now use the app!',
          [
            { 
              text: 'Continue', 
              onPress: () => {
                // Explicitly navigate to Home screen after profile setup
                navigation.reset({
                  index: 0,
                  routes: [{ name: 'Home' }],
                });
              }
            }
          ]
        );
      } else {
        Alert.alert('Success', 'Your profile has been updated successfully.');
      }
    } catch (error) {
      console.error('Error saving profile:', error);
      Alert.alert('Error', `Failed to save profile: ${error.message}`);
    } finally {
      setSaving(false);
    }
  };

  // ===== VERIFICATION FUNCTIONS =====

  // Load verification status
  const loadVerificationStatus = async () => {
    if (!user || !serverAddress) {
      return;
    }

    setIsLoadingVerification(true);
    try {
      // Get token from user object
      let token = null;
      const userJSON = await AsyncStorage.getItem('user');
      if (userJSON) {
        const userData = JSON.parse(userJSON);
        token = userData.token;
      }

      if (!token) {
        return;
      }

      const url = `http://${serverAddress}/api/verification/status`;
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setVerificationStatus(data);
      } else {
        console.log('Verification status error:', response.status);
        setVerificationStatus({
          isVerified: false,
          badgeType: null,
          verifiedAt: null,
          request: null
        });
      }
    } catch (error) {
      console.error('Error loading verification status:', error);
      setVerificationStatus({
        isVerified: false,
        badgeType: null,
        verifiedAt: null,
        request: null
      });
    } finally {
      setIsLoadingVerification(false);
    }
  };

  // Start pulse animation
  const startPulseAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.2,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  // Direct camera function that bypasses modal issues
  const openCameraDirect = async () => {
    console.log('� openCameraDirect called');
    try {
      // Request permissions first
      console.log('🔐 Requesting camera permissions...');
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      console.log('🔐 Camera permission status:', status);

      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Camera permission is required to take verification photos.');
        return;
      }

      console.log('📸 Opening native camera directly...');

      // Try the most basic camera call possible
      const result = await ImagePicker.launchCameraAsync({
        quality: 0.8,
        base64: true, // Ensure we get base64 data for admin panel
      });

      console.log('📸 Direct camera result:', result);

      if (!result.canceled && result.assets && result.assets[0]) {
        console.log('✅ Direct camera photo taken successfully');
        const asset = result.assets[0];
        // Use base64 if available, otherwise use URI
        const photoData = asset.base64
          ? `data:image/jpeg;base64,${asset.base64}`
          : asset.uri;
        console.log('📸 Photo data format:', photoData.startsWith('data:') ? 'base64' : 'URI');
        setVerificationPhoto(photoData);
        setShowVerificationModal(true);
      } else {
        console.log('� Camera was canceled or failed');
      }
    } catch (error) {
      console.error('❌ Direct camera error:', error);
      Alert.alert('Camera Error', `Failed to open camera: ${error.message}`);
    }
  };

  // Take verification photo with camera
  const takeVerificationPhoto = async () => {
    console.log('🎯 takeVerificationPhoto called');
    try {
      // Generate random number of fingers (1-5)
      const randomFingers = Math.floor(Math.random() * 5) + 1;
      console.log('🤚 Required fingers:', randomFingers);
      setRequiredFingers(randomFingers);

      // Show instruction modal first
      console.log('📱 Showing instruction modal');
      setShowInstructionModal(true);

    } catch (error) {
      console.error('❌ Error preparing verification photo:', error);
      Alert.alert('Error', 'Failed to prepare camera. Please try again.');
    }
  };



  // Open camera for verification photo
  const openCamera = async () => {
    console.log('🎥 openCamera called');
    try {
      if (Platform.OS === 'web') {
        console.log('📱 Web platform detected');
        // For web browsers, use HTML5 camera input
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'image/*';
        input.capture = 'environment'; // Use rear camera if available

        input.onchange = (event) => {
          const file = event.target.files[0];
          if (file) {
            const reader = new FileReader();
            reader.onload = (e) => {
              const base64Image = e.target.result;
              setVerificationPhoto(base64Image);
              setShowVerificationModal(true);
            };
            reader.readAsDataURL(file);
          }
        };

        input.click();
      } else {
        console.log('📱 Mobile platform detected');
        // For mobile (iOS/Android)
        console.log('🔐 Requesting camera permissions...');
        const { status } = await ImagePicker.requestCameraPermissionsAsync();
        console.log('🔐 Camera permission status:', status);

        if (status !== 'granted') {
          console.log('❌ Camera permission denied');
          Alert.alert('Permission Required', 'Sorry, we need camera permissions to take verification photos.');
          return;
        }

        console.log('📸 Launching camera...');
        console.log('📸 ImagePicker object:', ImagePicker);
        console.log('📸 launchCameraAsync function:', typeof ImagePicker.launchCameraAsync);

        // Launch camera with timeout
        console.log('📸 Using image library as workaround for camera issue...');

        // Since launchCameraAsync hangs on iOS, use image library instead
        // This will give user option to take photo or choose from library
        const result = await ImagePicker.launchImageLibraryAsync({
          mediaTypes: ImagePicker.MediaTypeOptions.Images,
          allowsEditing: true,
          aspect: [1, 1],
          quality: 0.8,
          base64: true,
        });

        console.log('📸 Image library result received');

        console.log('📸 Camera result received:', result);
        console.log('📸 Result type:', typeof result);
        console.log('📸 Result keys:', Object.keys(result));

        if (!result.canceled && result.assets && result.assets[0]) {
          console.log('✅ Photo taken successfully');
          const asset = result.assets[0];
          const base64Image = `data:image/jpeg;base64,${asset.base64}`;
          setVerificationPhoto(base64Image);
          setShowVerificationModal(true);
        } else {
          console.log('❌ Camera was canceled or no photo taken');
        }
      }
    } catch (error) {
      console.error('❌ Error taking verification photo:', error);
      Alert.alert('Error', 'Failed to take photo. Please try again.');
    }
  };

  // Convert image URI to base64
  const convertImageToBase64 = async (uri) => {
    try {
      if (uri.startsWith('data:')) {
        // Already base64
        console.log('📸 Photo is already base64 format');
        return uri;
      }

      console.log('📸 Converting URI to base64:', uri.substring(0, 50) + '...');

      if (Platform.OS === 'web') {
        // For web, use fetch and FileReader
        const response = await fetch(uri);
        const blob = await response.blob();

        return new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => resolve(reader.result);
          reader.onerror = reject;
          reader.readAsDataURL(blob);
        });
      } else {
        // For mobile, use expo-file-system if available, or fetch
        try {
          const response = await fetch(uri);
          const blob = await response.blob();

          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = reject;
            reader.readAsDataURL(blob);
          });
        } catch (fetchError) {
          console.error('Fetch failed, trying alternative method:', fetchError);
          // Fallback: return the URI as is and let the server handle it
          return uri;
        }
      }
    } catch (error) {
      console.error('Error converting image to base64:', error);
      // Fallback: return the original URI
      return uri;
    }
  };

  // Submit verification request
  const submitVerificationRequest = async () => {
    if (!verificationPhoto) {
      Alert.alert('Error', 'Please take a verification photo first.');
      return;
    }

    setIsLoadingVerification(true);
    try {
      // Get token from user object
      let token = null;
      const userJSON = await AsyncStorage.getItem('user');
      if (userJSON) {
        const userData = JSON.parse(userJSON);
        token = userData.token;
      }

      if (!token) {
        Alert.alert('Error', 'Please log in again.');
        return;
      }

      console.log('📸 Converting verification photo to base64...');
      // Convert photo to base64 for admin panel viewing
      const base64Photo = await convertImageToBase64(verificationPhoto);
      console.log('📸 Photo converted to base64, length:', base64Photo.length);

      const url = `http://${serverAddress}/api/verification/request`;
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          verificationPhoto: base64Photo,
          requiredFingers: requiredFingers || Math.floor(Math.random() * 5) + 1 // Fallback if not set
        }),
      });

      if (response.ok) {
        const data = await response.json();
        Alert.alert('Success', 'Verification request submitted successfully! We will review it within 24-48 hours.');
        setShowVerificationModal(false);
        setVerificationPhoto(null);
        setRequiredFingers(null);
        loadVerificationStatus(); // Refresh status
      } else {
        const error = await response.json();
        Alert.alert('Error', error.error || 'Failed to submit verification request');
      }
    } catch (error) {
      console.error('Error submitting verification request:', error);
      Alert.alert('Error', 'Failed to submit verification request. Please try again.');
    } finally {
      setIsLoadingVerification(false);
    }
  };

  // Render hand gesture based on finger count
  const renderFingerDisplay = (count) => {
    if (!count) return null;

    return (
      <View style={styles.fingerDisplayContainer}>
        <HandGesture fingers={count} size={120} color="#4e9af1" />
      </View>
    );
  };

  // Load verification status on mount
  useEffect(() => {
    if (user && serverAddress) {
      loadVerificationStatus();
    }
  }, [user, serverAddress]);

  // Handle camera opening after modal closes
  useEffect(() => {
    if (shouldOpenCamera && !showInstructionModal) {
      console.log('🎯 Modal closed, opening camera now...');
      setShouldOpenCamera(false);
      // Small delay to ensure UI is stable
      setTimeout(() => {
        openCameraDirect();
      }, 100);
    }
  }, [shouldOpenCamera, showInstructionModal]);

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4e9af1" />
        <Text style={styles.loadingText}>Loading profile...</Text>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.keyboardAvoidingView}
      behavior={Platform.OS === 'ios' ? 'padding' : null}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <ScrollView 
          style={styles.container}
          contentContainerStyle={styles.scrollContentContainer}
          keyboardShouldPersistTaps="handled"
        >
          {isInitialSetup && (
            <View style={styles.header}>
              <Text style={styles.requiredNote}>
                * Description, age, and at least 3 photos are required
              </Text>
            </View>
          )}

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Profile Photos</Text>
            <Text style={styles.sectionSubtitle}>
              {isInitialSetup
                ? <Text>Add at least 3 photos <Text style={styles.requiredStar}>*</Text></Text>
                : 'Add up to 4 photos'}
            </Text>
            {errors.photos && <Text style={styles.errorText}>{errors.photos}</Text>}


            <View style={styles.imagesContainer}>
              {images.map((image, index) => (
                <View key={index} style={styles.imageBox}>
                  {image ? (
                    <View style={styles.imageWrapper}>
                      <Image
                        source={{ uri: image.startsWith('data:') ? image : `data:image/jpeg;base64,${image}` }}
                        style={styles.image}
                      />

                      {/* Reorder buttons */}
                      <View style={styles.reorderButtons}>
                        {index > 0 && (
                          <TouchableOpacity
                            style={styles.reorderButton}
                            onPress={() => movePhoto(index, index - 1)}
                          >
                            <Ionicons name="chevron-back" size={16} color="#fff" />
                          </TouchableOpacity>
                        )}
                        {index < 3 && (
                          <TouchableOpacity
                            style={styles.reorderButton}
                            onPress={() => movePhoto(index, index + 1)}
                          >
                            <Ionicons name="chevron-forward" size={16} color="#fff" />
                          </TouchableOpacity>
                        )}
                      </View>

                      <TouchableOpacity
                        style={styles.removeButton}
                        onPress={() => handleRemoveImage(index)}
                      >
                        <Ionicons name="close-circle" size={24} color="#ff3b30" />
                      </TouchableOpacity>

                      {/* Photo number indicator */}
                      <View style={styles.photoNumber}>
                        <Text style={styles.photoNumberText}>{index + 1}</Text>
                      </View>
                    </View>
                  ) : (
                    <TouchableOpacity
                      style={styles.addImageButton}
                      onPress={() => handleImagePick(index)}
                    >
                      <Ionicons name="add" size={40} color="#4e9af1" />
                    </TouchableOpacity>
                  )}
                </View>
              ))}
            </View>
          </View>

          {/* Verification Section */}
          <View style={styles.section}>
            <View style={styles.verificationHeader}>
              <Text style={styles.sectionTitle}>Account Verification</Text>
              <TouchableOpacity
                style={styles.refreshButton}
                onPress={loadVerificationStatus}
                disabled={isLoadingVerification}
              >
                <Ionicons
                  name="refresh"
                  size={20}
                  color={isLoadingVerification ? "#ccc" : "#4e9af1"}
                />
              </TouchableOpacity>
            </View>

            {verificationStatus.isVerified ? (
              <View style={styles.verifiedContainer}>
                <View style={styles.verifiedHeader}>
                  <Ionicons
                    name="checkmark-circle"
                    size={24}
                    color={verificationStatus.badgeType === 'gold' ? '#FFD700' : '#4e9af1'}
                  />
                  <Text style={styles.verifiedText}>Account Verified</Text>
                  {verificationStatus.badgeType && (
                    <View style={[
                      styles.verificationBadge,
                      verificationStatus.badgeType === 'gold' ? styles.goldBadge : styles.blueBadge
                    ]}>
                      <Text style={[
                        styles.badgeText,
                        verificationStatus.badgeType === 'gold' ? styles.goldText : styles.blueText
                      ]}>
                        {verificationStatus.badgeType === 'gold' ? 'GOLD' : 'VERIFIED'}
                      </Text>
                    </View>
                  )}
                </View>
                <Text style={styles.verifiedDescription}>
                  Your account has been verified on {new Date(verificationStatus.verifiedAt).toLocaleDateString()}
                </Text>
              </View>
            ) : (
              <View style={styles.verificationContainer}>
                {verificationStatus.request ? (
                  <View style={styles.requestStatusContainer}>
                    <View style={styles.requestHeader}>
                      <Ionicons
                        name={verificationStatus.request.status === 'pending' ? 'time' :
                              verificationStatus.request.status === 'rejected' ? 'close-circle' : 'checkmark-circle'}
                        size={24}
                        color={verificationStatus.request.status === 'pending' ? '#FF9500' :
                               verificationStatus.request.status === 'rejected' ? '#FF3B30' : '#34C759'}
                      />
                      <Text style={styles.requestStatusText}>
                        Request {verificationStatus.request.status === 'pending' ? 'Pending' :
                                 verificationStatus.request.status === 'rejected' ? 'Rejected' : 'Approved'}
                      </Text>
                    </View>
                    <Text style={styles.requestDescription}>
                      {verificationStatus.request.status === 'pending'
                        ? 'Your verification request is being reviewed. This usually takes 24-48 hours.'
                        : verificationStatus.request.status === 'rejected'
                        ? 'Your verification request needs improvement.'
                        : 'Your verification request was approved!'}
                    </Text>

                    {verificationStatus.request.status === 'rejected' && (
                      <>
                        <Text style={styles.rejectionMessage}>
                          {verificationStatus.request.reviewNotes || 'Let\'s try again with a new photo'}
                        </Text>

                        {verificationStatus.request.requiredFingers && (
                          <Text style={styles.rejectionGesture}>
                            {(() => {
                              const fingerEmojis = { 1: '☝️', 2: '✌️', 3: '🤟', 4: '🖖', 5: '🖐️' };
                              return fingerEmojis[verificationStatus.request.requiredFingers] || '☝️';
                            })()} {verificationStatus.request.requiredFingers} finger{verificationStatus.request.requiredFingers > 1 ? 's' : ''}
                          </Text>
                        )}

                        <TouchableOpacity
                          style={styles.retryButton}
                          onPress={takeVerificationPhoto}
                          disabled={isLoadingVerification}
                        >
                          <Ionicons name="camera" size={20} color="#fff" style={{ marginRight: 8 }} />
                          <Text style={styles.retryButtonText}>Try Again</Text>
                        </TouchableOpacity>
                      </>
                    )}
                  </View>
                ) : (
                  <View style={styles.notVerifiedContainer}>
                    <View style={styles.verificationIconContainer}>
                      <Ionicons name="shield-checkmark-outline" size={48} color="#4e9af1" />
                    </View>
                    <TouchableOpacity
                      style={styles.verifyButton}
                      onPress={takeVerificationPhoto}
                      disabled={isLoadingVerification}
                    >
                      <Ionicons
                        name={isLoadingVerification ? "hourglass" : "camera"}
                        size={20}
                        color="#fff"
                      />
                    </TouchableOpacity>
                  </View>
                )}
              </View>
            )}
          </View>



          <View style={styles.section}>
            <Text style={styles.sectionTitle}>About You</Text>
            
            <View style={styles.formGroup}>
              <Text style={styles.label}>
                Age {<Text style={styles.requiredStar}>*</Text>}
              </Text>
              <TouchableOpacity
                style={[styles.input, styles.dropdownInput, errors.age && styles.inputError]}
                onPress={() => setShowAgeModal(true)}
              >
                <Text style={[styles.dropdownText, !age && styles.placeholderText]}>
                  {age || 'Select your age'}
                </Text>
                <Ionicons name="chevron-down" size={20} color="#666" />
              </TouchableOpacity>
              {errors.age && <Text style={styles.errorText}>{errors.age}</Text>}
            </View>
            
            <View style={styles.formGroup}>
              <Text style={styles.label}>
                Description <Text style={styles.requiredStar}>*</Text>
              </Text>
              <TextInput
                ref={descriptionRef}
                style={[styles.input, styles.textArea, errors.description && styles.inputError]}
                placeholder="Share a little about yourself..."
                value={description}
                onChangeText={setDescription}
                multiline
                numberOfLines={4}
                maxLength={500}
                returnKeyType="next"
                onSubmitEditing={() => passionsRef.current && passionsRef.current.focus()}
                blurOnSubmit={false}
                autoCorrect={false}
                autoCompleteType="off"
                textContentType="none"
                spellCheck={false}
              />
              <Text style={styles.charCount}>{description.length}/500</Text>
              {errors.description && <Text style={styles.errorText}>{errors.description}</Text>}
            </View>
            
            <View style={styles.formGroup}>
              <Text style={styles.label}>
                Passions <Text style={styles.requiredStar}>*</Text>
              </Text>
              <Text style={styles.helperText}>
                Select 3-6 interests that represent you.
              </Text>
              {errors.passions && <Text style={styles.errorText}>{errors.passions}</Text>}

              <View style={styles.passionContainer}>
                {PASSION_OPTIONS.map((passion) => (
                  <TouchableOpacity
                    key={passion}
                    style={[
                      styles.passionTag,
                      selectedPassions.includes(passion) && styles.selectedPassionTag,
                      selectedPassions.length >= 6 && !selectedPassions.includes(passion) && styles.disabledPassionTag
                    ]}
                    onPress={() => togglePassion(passion)}
                    disabled={selectedPassions.length >= 6 && !selectedPassions.includes(passion)}
                  >
                    <Text style={[
                      styles.passionTagText,
                      selectedPassions.includes(passion) && styles.selectedPassionTagText,
                      selectedPassions.length >= 6 && !selectedPassions.includes(passion) && styles.disabledPassionTagText
                    ]}>
                      {passion}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>

              <View style={styles.passionStatus}>
                <Text style={[
                  styles.selectedCount,
                  selectedPassions.length < 3 && styles.warningText,
                  selectedPassions.length >= 3 && selectedPassions.length <= 6 && styles.successText
                ]}>
                  {selectedPassions.length}/6 passions selected
                  {selectedPassions.length < 3 && ` (need ${3 - selectedPassions.length} more)`}
                </Text>
              </View>
            </View>
          </View>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.saveButton}
              onPress={handleSaveProfile}
              disabled={saving}
            >
              {saving ? (
                <ActivityIndicator color="#fff" size="small" />
              ) : (
                <Text style={styles.saveButtonText}>
                  {isInitialSetup ? 'Complete Profile' : 'Save Profile'}
                </Text>
              )}
            </TouchableOpacity>
          </View>
          
          {/* Add padding at the bottom for iOS keyboard */}
          {Platform.OS === 'ios' && <View style={styles.keyboardPadding} />}
        </ScrollView>
      </TouchableWithoutFeedback>

      {/* Age Selection Modal */}
      <Modal
        visible={showAgeModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowAgeModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Your Age</Text>
              <TouchableOpacity
                onPress={() => setShowAgeModal(false)}
                style={styles.modalCloseButton}
              >
                <Ionicons name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.ageList} showsVerticalScrollIndicator={false}>
              {Array.from({ length: 103 }, (_, i) => i + 18).map((ageOption) => (
                <TouchableOpacity
                  key={ageOption}
                  style={[
                    styles.ageOption,
                    age === ageOption.toString() && styles.selectedAgeOption
                  ]}
                  onPress={() => {
                    setAge(ageOption.toString());
                    setShowAgeModal(false);
                  }}
                >
                  <Text style={[
                    styles.ageOptionText,
                    age === ageOption.toString() && styles.selectedAgeOptionText
                  ]}>
                    {ageOption}
                  </Text>
                  {age === ageOption.toString() && (
                    <Ionicons name="checkmark" size={20} color="#4e9af1" />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* Instruction Modal */}
      <Modal
        visible={showInstructionModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowInstructionModal(false)}
      >
        <View style={styles.instructionModalOverlay}>
          <View style={styles.instructionModalContent}>
            <View style={styles.instructionModalHeader}>
              <Text style={styles.instructionModalTitle}>Verification Photo</Text>
              <TouchableOpacity
                onPress={() => setShowInstructionModal(false)}
                style={styles.modalCloseButton}
              >
                <Ionicons name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>

            <View style={styles.instructionContent}>
              <InstructionDisplay fingers={requiredFingers} size={200} />
            </View>

            <View style={styles.instructionButtons}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setShowInstructionModal(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.takePhotoButton}
                onPress={() => {
                  console.log('🎯 Take Photo button pressed');
                  console.log('🎯 Closing modal and using Alert approach...');
                  setShowInstructionModal(false);

                  // Use Alert approach which might work better
                  setTimeout(() => {
                    Alert.alert(
                      'Take Verification Photo',
                      `Hold up ${requiredFingers} finger${requiredFingers > 1 ? 's' : ''} and take a selfie`,
                      [
                        { text: 'Cancel', style: 'cancel' },
                        {
                          text: 'Open Camera',
                          onPress: async () => {
                            try {
                              console.log('📸 Alert camera button pressed');

                              const { status } = await ImagePicker.requestCameraPermissionsAsync();
                              if (status !== 'granted') {
                                Alert.alert('Permission Required', 'Camera permission is required');
                                return;
                              }

                              console.log('📸 Launching camera from Alert...');
                              const result = await ImagePicker.launchCameraAsync({
                                quality: 0.8,
                                base64: true, // Ensure we get base64 data
                              });

                              console.log('📸 Alert camera result:', result);

                              if (!result.canceled && result.assets && result.assets[0]) {
                                console.log('✅ Alert camera success');
                                const asset = result.assets[0];
                                // Use base64 if available, otherwise use URI
                                const photoData = asset.base64
                                  ? `data:image/jpeg;base64,${asset.base64}`
                                  : asset.uri;
                                console.log('📸 Photo data format:', photoData.startsWith('data:') ? 'base64' : 'URI');
                                setVerificationPhoto(photoData);
                                setShowVerificationModal(true);
                              }
                            } catch (error) {
                              console.error('❌ Alert camera error:', error);
                              Alert.alert('Error', `Camera failed: ${error.message}`);
                            }
                          }
                        }
                      ]
                    );
                  }, 300);
                }}
              >
                <Ionicons name="camera" size={20} color="#fff" style={{ marginRight: 8 }} />
                <Text style={styles.takePhotoButtonText}>Take Photo</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Finger Animation Modal */}
      <Modal
        visible={showFingerAnimation}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowFingerAnimation(false)}
      >
        <View style={styles.fingerAnimationOverlay}>
          <View style={styles.fingerAnimationContainer}>
            <Animated.View style={[styles.fingerDisplay, { transform: [{ scale: pulseAnim }] }]}>
              {renderFingerDisplay(requiredFingers)}
            </Animated.View>
            <View style={styles.cameraIcon}>
              <Ionicons name="camera" size={40} color="#4e9af1" />
            </View>
          </View>
        </View>
      </Modal>

      {/* Verification Photo Modal */}
      <Modal
        visible={showVerificationModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowVerificationModal(false)}
      >
        <View style={styles.verificationModalContainer}>
          <View style={styles.verificationModalHeader}>
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setShowVerificationModal(false)}
            >
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>
            <View style={styles.headerCenter}>
              <Ionicons name="shield-checkmark" size={24} color="#4e9af1" />
            </View>
            <TouchableOpacity
              style={[styles.submitButton, { opacity: verificationPhoto ? 1 : 0.5 }]}
              onPress={submitVerificationRequest}
              disabled={!verificationPhoto || isLoadingVerification}
            >
              <Ionicons
                name={isLoadingVerification ? "hourglass" : "checkmark"}
                size={16}
                color="#fff"
              />
            </TouchableOpacity>
          </View>

          <ScrollView
            style={styles.verificationModalContent}
            contentContainerStyle={styles.verificationModalScrollContent}
            showsVerticalScrollIndicator={false}
          >
            {/* Visual finger requirement display */}
            <View style={styles.fingerRequirementDisplay}>
              {renderFingerDisplay(requiredFingers)}
              {requiredFingers && (
                <Text style={styles.fingerInstructionText}>
                  {(() => {
                    const fingerEmojis = { 1: '☝️', 2: '✌️', 3: '🤟', 4: '🖖', 5: '🖐️' };
                    return fingerEmojis[requiredFingers] || '☝️';
                  })()} Hold up {requiredFingers} finger{requiredFingers > 1 ? 's' : ''}
                </Text>
              )}
            </View>

            {verificationPhoto && (
              <View style={styles.photoPreviewContainer}>
                <Image source={{ uri: verificationPhoto }} style={styles.photoPreview} />
                <TouchableOpacity
                  style={styles.retakePhotoButton}
                  onPress={openCamera}
                >
                  <Ionicons name="camera" size={20} color="#4e9af1" />
                </TouchableOpacity>
              </View>
            )}

            {/* Tips with icons and text */}
            <View style={styles.verificationTips}>
              <Text style={styles.tipsTitle}>Tips for verification:</Text>
              <View style={styles.tipRow}>
                <Ionicons name="sunny" size={16} color="#FFD700" />
                <Text style={styles.tipText}>Good lighting</Text>
              </View>
              <View style={styles.tipRow}>
                <Ionicons name="eye" size={16} color="#4e9af1" />
                <Text style={styles.tipText}>Face clearly visible</Text>
              </View>
              <View style={styles.tipRow}>
                <Ionicons name="checkmark-circle" size={16} color="#34C759" />
                <Text style={styles.tipText}>Hold steady</Text>
              </View>
            </View>
          </ScrollView>
        </View>
      </Modal>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  keyboardAvoidingView: {
    flex: 1,
  },
  container: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  scrollContentContainer: {
    paddingBottom: Platform.OS === 'ios' ? 100 : 30, // Extra padding for iOS
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#666',
  },
  requiredNote: {
    fontSize: 14,
    color: '#ff3b30',
    marginTop: 5,
  },
  requiredStar: {
    color: '#ff3b30',
    fontWeight: 'bold',
  },
  section: {
    backgroundColor: '#fff',
    marginTop: 15,
    padding: 20,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#eee',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 5,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 15,
  },
  imagesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  imageBox: {
    width: '48%',
    aspectRatio: 1,
    marginBottom: 10,
    borderRadius: 8,
    overflow: 'hidden',
  },
  imageWrapper: {
    width: '100%',
    height: '100%',
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  removeButton: {
    position: 'absolute',
    top: 5,
    right: 5,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 12,
  },
  addImageButton: {
    width: '100%',
    height: '100%',
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#e0e0e0',
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
  },
  formGroup: {
    marginBottom: 30, // Increased space between form groups
    zIndex: 1,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
    color: '#333',
  },
  input: {
    backgroundColor: '#f5f5f5',
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    padding: 8,
    fontSize: 14,
  },
  inputError: {
    borderColor: '#ff3b30',
    borderWidth: 1,
  },
  errorText: {
    color: '#ff3b30',
    fontSize: 12,
    marginTop: 5,
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  charCount: {
    alignSelf: 'flex-end',
    fontSize: 12,
    color: '#999',
    marginTop: 5,
  },
  helperText: {
    fontSize: 12,
    color: '#666',
    marginTop: 5,
  },
  buttonContainer: {
    padding: 20,
    marginBottom: 30,
  },
  saveButton: {
    backgroundColor: '#4e9af1',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
  keyboardPadding: {
    height: 60, // Extra padding for iOS keyboard
  },
  // Age dropdown styles
  dropdownInput: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: Platform.OS === 'ios' ? 12 : 10,
  },
  dropdownText: {
    fontSize: 14,
    color: '#2c384a',
    fontWeight: '500',
  },
  placeholderText: {
    color: '#8a9cb0',
    fontWeight: '400',
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '70%',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  modalCloseButton: {
    padding: 5,
  },
  ageList: {
    maxHeight: 300,
  },
  ageOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  selectedAgeOption: {
    backgroundColor: '#f0f8ff',
  },
  ageOptionText: {
    fontSize: 16,
    color: '#333',
  },
  selectedAgeOptionText: {
    color: '#4e9af1',
    fontWeight: '600',
  },
  // Passion tags styles
  passionContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 10,
  },
  passionTag: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    margin: 2,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  selectedPassionTag: {
    backgroundColor: '#4e9af1',
    borderColor: '#4e9af1',
  },
  passionTagText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  selectedPassionTagText: {
    color: '#fff',
    fontWeight: '600',
  },
  disabledPassionTag: {
    backgroundColor: '#f8f8f8',
    borderColor: '#e8e8e8',
    opacity: 0.5,
  },
  disabledPassionTagText: {
    color: '#ccc',
  },
  passionStatus: {
    marginTop: 10,
    alignItems: 'center',
  },
  selectedCount: {
    fontSize: 12,
    color: '#4e9af1',
    marginTop: 8,
    fontWeight: '500',
  },
  warningText: {
    color: '#ff9500',
  },
  successText: {
    color: '#34c759',
  },

  // Photo reordering styles
  reorderButtons: {
    position: 'absolute',
    bottom: 8,
    left: 8,
    right: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  reorderButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 15,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  photoNumber: {
    position: 'absolute',
    top: 8,
    left: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  photoNumberText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },

  // Verification styles
  verificationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  refreshButton: {
    padding: 5,
  },
  verifiedContainer: {
    backgroundColor: '#f0f9ff',
    padding: 15,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#e0f2fe',
  },
  verifiedHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  verifiedText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginLeft: 8,
    flex: 1,
  },
  verificationBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  blueBadge: {
    backgroundColor: 'rgba(78, 154, 241, 0.1)',
  },
  goldBadge: {
    backgroundColor: 'rgba(255, 215, 0, 0.1)',
  },
  badgeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  blueText: {
    color: '#4e9af1',
  },
  goldText: {
    color: '#FFD700',
  },
  verifiedDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 5,
  },
  verificationContainer: {
    backgroundColor: '#f8f9fa',
    padding: 15,
    borderRadius: 10,
  },
  requestStatusContainer: {
    alignItems: 'center',
  },
  requestHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  requestStatusText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
    color: '#333',
  },
  requestDescription: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 15,
  },
  resubmitButton: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#4e9af1',
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  notVerifiedContainer: {
    alignItems: 'center',
  },
  verificationIconContainer: {
    marginBottom: 20,
    alignItems: 'center',
  },
  verifyButton: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#4e9af1',
    width: 60,
    height: 60,
    borderRadius: 30,
  },

  // Finger animation styles
  fingerAnimationOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  fingerAnimationContainer: {
    backgroundColor: '#fff',
    borderRadius: 20,
    padding: 40,
    alignItems: 'center',
    minWidth: 300,
  },
  fingerDisplay: {
    marginBottom: 30,
  },
  fingersContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    flexWrap: 'wrap',
    gap: 10,
  },
  fingerIcon: {
    margin: 5,
  },
  cameraIcon: {
    marginTop: 20,
  },

  // Verification modal styles
  verificationModalContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  verificationModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerCenter: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  submitButton: {
    backgroundColor: '#4e9af1',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  verificationModalContent: {
    flex: 1,
  },
  verificationModalScrollContent: {
    padding: 15,
    alignItems: 'center',
    minHeight: '100%',
  },
  fingerRequirementDisplay: {
    marginBottom: 20,
    alignItems: 'center',
  },
  fingerDisplayContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 10,
  },
  fingerInstructionText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#4e9af1',
    marginTop: 10,
    textAlign: 'center',
  },
  tipRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  tipsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  tipText: {
    fontSize: 13,
    color: '#666',
    marginLeft: 6,
  },
  verificationTips: {
    backgroundColor: '#f8f9fa',
    padding: 12,
    borderRadius: 8,
    marginTop: 15,
    width: '100%',
  },
  photoPreviewContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  photoPreview: {
    width: 180,
    height: 180,
    borderRadius: 12,
    marginBottom: 12,
  },
  retakePhotoButton: {
    backgroundColor: '#f0f0f0',
    padding: 10,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  visualTips: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    width: '100%',
    marginTop: 20,
  },
  tipIcon: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#f8f9fa',
  },

  // Instruction modal styles
  instructionModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  instructionModalContent: {
    backgroundColor: '#fff',
    borderRadius: 20,
    width: '90%',
    maxHeight: '80%',
  },
  instructionModalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  instructionModalTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
  },
  instructionContent: {
    padding: 30,
    alignItems: 'center',
  },
  instructionButtons: {
    flexDirection: 'row',
    padding: 20,
    gap: 15,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#f0f0f0',
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666',
  },
  takePhotoButton: {
    flex: 2,
    backgroundColor: '#4e9af1',
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  takePhotoButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },

  // Rejection UI styles - Clean and integrated
  rejectionMessage: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 15,
    marginBottom: 15,
    lineHeight: 22,
    fontStyle: 'italic',
  },
  rejectionGesture: {
    fontSize: 28,
    textAlign: 'center',
    marginBottom: 20,
    color: '#4e9af1',
    fontWeight: '700',
  },
  retryButton: {
    backgroundColor: '#4e9af1',
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 10,
    shadowColor: '#4e9af1',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
});

export default ProfileScreen;