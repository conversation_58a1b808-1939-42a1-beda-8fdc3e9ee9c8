// screens/SettingsScreen.js
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Switch,
  TextInput,
  Alert,
  ScrollView,
  FlatList,
  Platform,
  Linking,
  Modal,
  Image
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Slider from '@react-native-community/slider';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Location from 'expo-location';

import { WebView } from 'react-native-webview';

const SettingsScreen = ({
  navigation,
  maxDistance,
  minAge = 18,
  maxAge = 100,
  blockedUsers = [],
  onUpdateMaxDistance,
  onUpdateAgeRange,
  onUnblockUser,
  onLogout,
  user,
  serverAddress
}) => {
 
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [distance, setDistance] = useState(maxDistance);
  const [minimumAge, setMinimumAge] = useState(minAge);
  const [maximumAge, setMaximumAge] = useState(maxAge);
  const [blockedUsernames, setBlockedUsernames] = useState({});
  const [locationPermission, setLocationPermission] = useState(null);
  const [isCheckingLocation, setIsCheckingLocation] = useState(false);

  // Gender and matching preferences
  const [userGender, setUserGender] = useState('');
  const [interestedIn, setInterestedIn] = useState('both');
  const [lookingFor, setLookingFor] = useState('relationship'); // 'relationship' or 'friends'

  // Premium-related state
  const [premiumStatus, setPremiumStatus] = useState({
    isPremium: false,
    subscriptionType: null,
    subscriptionStart: null,
    subscriptionEnd: null,
    daysRemaining: 0,
    customLocation: {
      enabled: false,
      city: '',
      country: '',
      coordinates: null
    }
  });
  const [customLocation, setCustomLocation] = useState({
    enabled: false,
    city: '',
    country: '',
    coordinates: null
  });
  const [isLoadingPremium, setIsLoadingPremium] = useState(false);
  const [showLocationModal, setShowLocationModal] = useState(false);
  const [showMapModal, setShowMapModal] = useState(false);
  const [newCity, setNewCity] = useState('');
  const [newCountry, setNewCountry] = useState('');
  const [selectedMapLocation, setSelectedMapLocation] = useState(null);

  // Check location permission status and load premium status on mount
  useEffect(() => {
    checkLocationPermission();
    loadUserPreferences();
    if (user && serverAddress) {
      loadPremiumStatus();
    }
  }, [user, serverAddress]);

  // Load user preferences from storage and server
  const loadUserPreferences = async () => {
    try {
      // First load from local storage
      const savedGender = await AsyncStorage.getItem('userGender');
      const savedInterestedIn = await AsyncStorage.getItem('interestedIn');
      const savedLookingFor = await AsyncStorage.getItem('lookingFor');

      if (savedGender) setUserGender(savedGender);
      if (savedInterestedIn) setInterestedIn(savedInterestedIn);
      if (savedLookingFor) setLookingFor(savedLookingFor);

      // Then try to load from server if user is logged in
      if (user && serverAddress) {
        try {
          // Get token from user object, not AsyncStorage
          const token = user.token;

          // Ensure server address has protocol
          const fullServerAddress = serverAddress.startsWith('http') ? serverAddress : `http://${serverAddress}`;

          console.log('📋 Settings: Loading preferences from server:', `${fullServerAddress}/api/user/preferences`);
          console.log('🔑 Settings: Using token:', token ? 'Token present' : 'No token');

          const response = await fetch(`${fullServerAddress}/api/user/preferences`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });

          console.log('📡 Settings: Response status:', response.status);

          if (response.ok) {
            const serverPreferences = await response.json();
            console.log('✅ Settings: Server preferences loaded:', serverPreferences);

            // Update state with server data
            setUserGender(serverPreferences.gender || '');
            setInterestedIn(serverPreferences.interestedIn || 'both');
            setLookingFor(serverPreferences.lookingFor || 'relationship');

            // Update local storage with server data
            await AsyncStorage.setItem('userGender', serverPreferences.gender || '');
            await AsyncStorage.setItem('interestedIn', serverPreferences.interestedIn || 'both');
            await AsyncStorage.setItem('lookingFor', serverPreferences.lookingFor || 'relationship');
          } else {
            const errorText = await response.text();
            console.log('❌ Settings: Server response not ok:', response.status, response.statusText);
            console.log('❌ Settings: Error response body:', errorText);
          }
        } catch (serverError) {
          console.error('❌ Settings: Error loading preferences from server:', serverError);
          console.error('Settings error details:', serverError.message);
        }
      }
    } catch (error) {
      console.error('Error loading user preferences:', error);
    }
  };

  // Save user preferences to storage and server
  const saveUserPreferences = async (gender, interested, looking) => {
    try {
      // Save to local storage
      await AsyncStorage.setItem('userGender', gender);
      await AsyncStorage.setItem('interestedIn', interested);
      await AsyncStorage.setItem('lookingFor', looking);

      // Save to server if user is logged in
      if (user && serverAddress) {
        try {
          // Get token from user object, not AsyncStorage
          const token = user.token;

          // Ensure server address has protocol
          const fullServerAddress = serverAddress.startsWith('http') ? serverAddress : `http://${serverAddress}`;

          console.log('💾 Saving preferences to server:', `${fullServerAddress}/api/user/preferences`);

          const response = await fetch(`${fullServerAddress}/api/user/preferences`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
              gender: gender,
              interestedIn: interested,
              lookingFor: looking
            })
          });

          if (!response.ok) {
            const errorText = await response.text();
            console.error('❌ Failed to save preferences to server:', response.status);
            console.error('❌ Error response body:', errorText);
          } else {
            console.log('✅ Preferences saved to server successfully');
          }
        } catch (serverError) {
          console.error('❌ Error saving preferences to server:', serverError);
        }
      }
    } catch (error) {
      console.error('Error saving user preferences:', error);
    }
  };

  // Check location permission
  const checkLocationPermission = async () => {
    setIsCheckingLocation(true);
    try {
      const { status } = await Location.getForegroundPermissionsAsync();
      setLocationPermission(status);
    } catch (error) {
      console.error('Error checking location permission:', error);
    } finally {
      setIsCheckingLocation(false);
    }
  };

  // Request location permission
  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      setLocationPermission(status);
      
      if (status !== 'granted') {
        // If permission denied, show alert with option to open settings
        Alert.alert(
          "Location Permission Required",
          "Shake & Match needs location permission to find matches near you. Please enable location in your device settings.",
          [
            { text: "Cancel", style: "cancel" },
            { 
              text: "Open Settings", 
              onPress: () => {
                if (Platform.OS === 'ios') {
                  Linking.openURL('app-settings:');
                } else {
                  Linking.openSettings();
                }
              } 
            }
          ]
        );
      } else {
        Alert.alert(
          "Location Enabled",
          "Location permission granted! You can now match with nearby users."
        );
      }
    } catch (error) {
      console.error('Error requesting location permission:', error);
    }
  };

  // Load blocked user profiles
  useEffect(() => {
    const loadBlockedUserProfiles = async () => {
      try {
        const profilesObj = {};

        for (const userId of blockedUsers) {
          // First try to get from local storage
          const cachedProfile = await AsyncStorage.getItem(`profile_${userId}`);
          if (cachedProfile) {
            try {
              profilesObj[userId] = JSON.parse(cachedProfile);
              continue;
            } catch (parseError) {
              console.log('Error parsing cached profile for user:', userId);
            }
          }

          // If not in cache or user/server available, try to fetch from server
          if (user && serverAddress) {
            try {
              const token = user.token;
              const fullServerAddress = serverAddress.startsWith('http') ? serverAddress : `http://${serverAddress}`;

              const response = await fetch(`${fullServerAddress}/api/profile/${userId}`, {
                method: 'GET',
                headers: {
                  'Authorization': `Bearer ${token}`,
                  'Content-Type': 'application/json'
                }
              });

              if (response.ok) {
                const profile = await response.json();
                profilesObj[userId] = profile;

                // Cache the profile
                await AsyncStorage.setItem(`profile_${userId}`, JSON.stringify(profile));
              } else {
                // Fallback to stored username or default
                const storedUsername = await AsyncStorage.getItem(`username_${userId}`);
                profilesObj[userId] = {
                  username: storedUsername || 'Unknown User',
                  images: []
                };
              }
            } catch (fetchError) {
              console.log('Error fetching profile for blocked user:', userId, fetchError.message);
              // Fallback to stored username or default
              const storedUsername = await AsyncStorage.getItem(`username_${userId}`);
              profilesObj[userId] = {
                username: storedUsername || 'Unknown User',
                images: []
              };
            }
          } else {
            // No server connection, use stored username
            const storedUsername = await AsyncStorage.getItem(`username_${userId}`);
            profilesObj[userId] = {
              username: storedUsername || 'Unknown User',
              images: []
            };
          }
        }

        setBlockedUsernames(profilesObj);
      } catch (error) {
        console.error('Error loading blocked user profiles:', error);
      }
    };

    if (blockedUsers.length > 0) {
      loadBlockedUserProfiles();
    } else {
      setBlockedUsernames({});
    }
  }, [blockedUsers, user, serverAddress]);

  // Handle unblock user button press
  const handleUnblockUser = (userId) => {
    Alert.alert(
      'Unblock User',
      `Are you sure you want to unblock ${blockedUsernames[userId] || 'this user'}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Unblock', 
          style: 'destructive',
          onPress: () => {
            if (onUnblockUser) {
              onUnblockUser(userId);
            }
          }
        }
      ]
    );
  };

  // Handle distance change
  const handleDistanceChange = (value) => {
    setDistance(value);
    onUpdateMaxDistance(value);
  };

  // Handle min age change
  const handleMinAgeChange = (value) => {
    // Set the value directly from the slider
    setMinimumAge(value);
    
    // If the minimum age is now greater than or equal to maximum age, 
    // also increase the maximum age
    if (value >= maximumAge) {
      const newMaxAge = value + 1;
      setMaximumAge(newMaxAge);
      
      if (onUpdateAgeRange) {
        onUpdateAgeRange(value, newMaxAge);
      }
    } else {
      // Otherwise just update the minimum age
      if (onUpdateAgeRange) {
        onUpdateAgeRange(value, maximumAge);
      }
    }
  };

  // Handle max age change
  const handleMaxAgeChange = (value) => {
    // Set the value directly from the slider
    setMaximumAge(value);
    
    // If the maximum age is now less than or equal to minimum age, 
    // also decrease the minimum age
    if (value <= minimumAge) {
      const newMinAge = value - 1;
      setMinimumAge(newMinAge);
      
      if (onUpdateAgeRange) {
        onUpdateAgeRange(newMinAge, value);
      }
    } else {
      // Otherwise just update the maximum age
      if (onUpdateAgeRange) {
        onUpdateAgeRange(minimumAge, value);
      }
    }
  };

  // Handle logout
  const handleLogout = () => {
    Alert.alert(
      'Confirm Logout',
      'Are you sure you want to log out?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Logout', style: 'destructive', onPress: onLogout }
      ]
    );
  };

  // Load premium status
  const loadPremiumStatus = async () => {
    console.log('Loading premium status...', {
      user: !!user,
      userId: user?.id,
      username: user?.username,
      serverAddress
    });
    if (!user || !serverAddress) {
      console.log('Missing user or serverAddress');
      return;
    }

    setIsLoadingPremium(true);
    try {
      // Try to get token from user object first (mobile app storage method)
      let token = null;
      const userJSON = await AsyncStorage.getItem('user');
      if (userJSON) {
        const userData = JSON.parse(userJSON);
        token = userData.token;
        console.log('Token from user object:', !!token);
      }

      // Fallback to separate authToken storage (web app method)
      if (!token) {
        token = await AsyncStorage.getItem('authToken');
        console.log('Token from authToken key:', !!token);
      }

      if (!token) {
        console.log('No auth token found in either location');
        return;
      }

      const url = `http://${serverAddress}/api/premium/status`;
      console.log('Fetching premium status from:', url);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      console.log('Premium status response:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('Premium status data received:', JSON.stringify(data, null, 2));
        setPremiumStatus(data);
        setCustomLocation(data.customLocation || {
          enabled: false,
          city: '',
          country: '',
          coordinates: null
        });
        console.log('Premium status set:', data.isPremium);
      } else {
        const errorText = await response.text();
        console.log('Premium status error:', response.status, response.statusText, errorText);
        // Set a default premium status for testing
        setPremiumStatus({
          isPremium: false,
          customLocation: { enabled: false }
        });
      }
    } catch (error) {
      console.error('Error loading premium status:', error);
      // Set a default premium status for testing
      setPremiumStatus({
        isPremium: false,
        customLocation: { enabled: false }
      });
    } finally {
      setIsLoadingPremium(false);
    }
  };

  // Update custom location
  const updateCustomLocation = async () => {
    if (!newCity.trim() || !newCountry.trim()) {
      Alert.alert('Error', 'Please enter both city and country');
      return;
    }

    setIsLoadingPremium(true);
    try {
      // Get token from user object
      let token = null;
      const userJSON = await AsyncStorage.getItem('user');
      if (userJSON) {
        const userData = JSON.parse(userJSON);
        token = userData.token;
      }
      if (!token) return;

      // For demo purposes, we'll use approximate coordinates
      // In a real app, you'd use a geocoding service
      const mockCoordinates = {
        latitude: 40.7128 + (Math.random() - 0.5) * 10,
        longitude: -74.0060 + (Math.random() - 0.5) * 10
      };

      const response = await fetch(`http://${serverAddress}/api/premium/location`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          city: newCity.trim(),
          country: newCountry.trim(),
          latitude: mockCoordinates.latitude,
          longitude: mockCoordinates.longitude
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setCustomLocation(data.customLocation);
        setShowLocationModal(false);
        setNewCity('');
        setNewCountry('');
        Alert.alert('Success', 'Custom location updated successfully!');
        loadPremiumStatus(); // Refresh status
      } else {
        const error = await response.json();
        Alert.alert('Error', error.error || 'Failed to update location');
      }
    } catch (error) {
      console.error('Error updating custom location:', error);
      Alert.alert('Error', 'Failed to update location');
    } finally {
      setIsLoadingPremium(false);
    }
  };

  // Disable custom location
  const disableCustomLocation = async () => {
    setIsLoadingPremium(true);
    try {
      // Get token from user object
      let token = null;
      const userJSON = await AsyncStorage.getItem('user');
      if (userJSON) {
        const userData = JSON.parse(userJSON);
        token = userData.token;
      }
      if (!token) return;

      const response = await fetch(`http://${serverAddress}/api/premium/location`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        setCustomLocation({
          enabled: false,
          city: '',
          country: '',
          coordinates: null
        });
        Alert.alert('Success', 'Custom location disabled. Using your real location now.');
        loadPremiumStatus(); // Refresh status
      } else {
        const error = await response.json();
        Alert.alert('Error', error.error || 'Failed to disable custom location');
      }
    } catch (error) {
      console.error('Error disabling custom location:', error);
      Alert.alert('Error', 'Failed to disable custom location');
    } finally {
      setIsLoadingPremium(false);
    }
  };

  // Open coordinate-based location picker
  const openCoordinateLocationPicker = async () => {
    try {
      // Get current location as default
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        const location = await Location.getCurrentPositionAsync({});
        setSelectedMapLocation({
          latitude: location.coords.latitude,
          longitude: location.coords.longitude
        });
      } else {
        // Default to a central location if no permission
        setSelectedMapLocation({
          latitude: 40.7128,
          longitude: -74.0060
        });
      }
    } catch (error) {
      console.log('Error getting current location:', error);
      // Default to New York coordinates
      setSelectedMapLocation({
        latitude: 40.7128,
        longitude: -74.0060
      });
    }

    setShowMapModal(true);
  };

  // Reverse geocode selected location
  const reverseGeocodeLocation = async (latitude, longitude) => {
    try {
      const results = await Location.reverseGeocodeAsync({
        latitude,
        longitude
      });

      if (results.length > 0) {
        const result = results[0];
        return {
          city: result.city || result.subregion || result.region || 'Unknown City',
          country: result.country || 'Unknown Country'
        };
      }
    } catch (error) {
      console.error('Error reverse geocoding:', error);
    }

    return {
      city: 'Custom Location',
      country: 'Unknown'
    };
  };

  // Update custom location with specific coordinates
  const updateCustomLocationWithCoords = async (city, country, latitude, longitude) => {
    if (!user || !serverAddress) return false;

    try {
      // Get token from user object
      let token = null;
      const userJSON = await AsyncStorage.getItem('user');
      if (userJSON) {
        const userData = JSON.parse(userJSON);
        token = userData.token;
      }
      if (!token) return false;

      const response = await fetch(`http://${serverAddress}/api/premium/location`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          city: city.trim(),
          country: country.trim(),
          latitude,
          longitude
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setCustomLocation(data.customLocation);
        Alert.alert('Success', `Custom location set to ${city}, ${country}`);
        loadPremiumStatus(); // Refresh status
        return true;
      } else {
        const error = await response.json();
        Alert.alert('Error', error.error || 'Failed to update location');
        return false;
      }
    } catch (error) {
      console.error('Error updating custom location:', error);
      Alert.alert('Error', 'Failed to update location');
      return false;
    }
  };

  // Handle messages from the Google Maps WebView
  const handleMapMessage = (event) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      console.log('Map message received:', data);

      if (data.type === 'locationSelected') {
        setSelectedMapLocation({
          latitude: data.lat,
          longitude: data.lng,
          city: data.address || 'Custom Location'
        });
      }
    } catch (error) {
      console.error('Error parsing map message:', error);
    }
  };

  // Generate HTML for OpenStreetMap (free, no API key needed)
  const getGoogleMapsHTML = () => {
    const currentLat = selectedMapLocation?.latitude || 40.7128;
    const currentLng = selectedMapLocation?.longitude || -74.0060;

    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Location Picker</title>
        <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
        <style>
            body, html {
                margin: 0;
                padding: 0;
                height: 100%;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }
            #map {
                height: 100%;
                width: 100%;
            }
          .reset-button-container {
              position: absolute;
              top: 20px;
              left: 50%;
              transform: translateX(-50%);
              z-index: 1000;
          }
            #resetButton {
                background: rgba(255, 255, 255, 0.95);
                color: #333;
                border: 1px solid rgba(0, 0, 0, 0.1);
                padding: 12px 16px;
                border-radius: 12px;
                font-size: 13px;
                font-weight: 600;
                cursor: pointer;
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
                backdrop-filter: blur(10px);
                transition: all 0.2s ease;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }
            #resetButton:hover {
                background: rgba(255, 255, 255, 1);
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
            }
            #resetButton:active {
                transform: translateY(0);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            }
            #resetButton:disabled {
                opacity: 0.6;
                cursor: not-allowed;
                transform: none;
            }
            .leaflet-control-attribution {
                font-size: 10px !important;
            }
        </style>
    </head>
    <body>
        <div class="reset-button-container">
            <button id="resetButton" onclick="resetToRealLocation()">📍 Use My Real Location</button>
        </div>
        <div id="map"></div>

        <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
        <script>
            let map;
            let marker;
            let selectedLat = ${currentLat};
            let selectedLng = ${currentLng};

            // Initialize the map
            map = L.map('map').setView([selectedLat, selectedLng], 4);

            // Add OpenStreetMap tiles (free, no API key needed)
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors',
                maxZoom: 19
            }).addTo(map);

            // Add satellite imagery option
            const satelliteLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                attribution: '© Esri, Maxar, GeoEye, Earthstar Geographics, CNES/Airbus DS, USDA, USGS, AeroGRID, IGN, and the GIS User Community',
                maxZoom: 19
            });

            // Layer control
            const baseMaps = {
                "Street Map": L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors'
                }),
                "Satellite": satelliteLayer
            };

            L.control.layers(baseMaps).addTo(map);

            // Custom marker icon
            const customIcon = L.divIcon({
                html: \`
                    <div style="
                        width: 30px;
                        height: 40px;
                        position: relative;
                        filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.3));
                    ">
                        <svg width="30" height="40" viewBox="0 0 30 40">
                            <path d="M15 0C6.716 0 0 6.716 0 15c0 15 15 25 15 25s15-10 15-25C30 6.716 23.284 0 15 0z" fill="#FF4444"/>
                            <circle cx="15" cy="15" r="7" fill="white"/>
                            <circle cx="15" cy="15" r="3" fill="#FF4444"/>
                        </svg>
                    </div>
                \`,
                className: 'custom-marker',
                iconSize: [30, 40],
                iconAnchor: [15, 40]
            });

            // Add initial marker if we have a selected location
            if (selectedLat && selectedLng) {
                marker = L.marker([selectedLat, selectedLng], {icon: customIcon}).addTo(map);
            }

            // Handle map clicks
            map.on('click', function(e) {
                const lat = e.latlng.lat;
                const lng = e.latlng.lng;

                console.log('Map clicked at:', lat, lng);

                // Remove existing marker
                if (marker) {
                    map.removeLayer(marker);
                }

                // Add new marker (don't center map - let user stay where they are)
                marker = L.marker([lat, lng], {icon: customIcon}).addTo(map);

                // Always send location data immediately
                const locationData = {
                    type: 'locationSelected',
                    lat: lat,
                    lng: lng,
                    address: 'Custom Location'
                };

                console.log('Sending location data:', locationData);
                window.ReactNativeWebView.postMessage(JSON.stringify(locationData));

                // Try to get address using Nominatim (free reverse geocoding) - this is optional
                fetch(\`https://nominatim.openstreetmap.org/reverse?format=json&lat=\${lat}&lon=\${lng}&zoom=18&addressdetails=1\`)
                    .then(response => response.json())
                    .then(data => {
                        if (data && data.display_name) {
                            // Send updated location data with real address
                            const updatedLocationData = {
                                type: 'locationSelected',
                                lat: lat,
                                lng: lng,
                                address: data.display_name
                            };

                            console.log('Sending updated location data with address:', updatedLocationData);
                            window.ReactNativeWebView.postMessage(JSON.stringify(updatedLocationData));
                        }
                    })
                    .catch(error => {
                        console.log('Reverse geocoding failed:', error);
                        // Location data was already sent above, so this is fine
                        window.ReactNativeWebView.postMessage(JSON.stringify({
                            type: 'locationSelected',
                            lat: lat,
                            lng: lng,
                            address: 'Custom Location'
                        }));
                    });
            });

            // Add zoom controls
            L.control.zoom({
                position: 'bottomright'
            }).addTo(map);

            // Reset to real location function
            function resetToRealLocation() {
                if (navigator.geolocation) {
                    document.getElementById('resetButton').textContent = '📍 Getting location...';
                    document.getElementById('resetButton').disabled = true;

                    const options = {
                        enableHighAccuracy: true,
                        timeout: 10000,
                        maximumAge: 60000
                    };

                    navigator.geolocation.getCurrentPosition(function(position) {
                        const lat = position.coords.latitude;
                        const lng = position.coords.longitude;

                        console.log('Got real location:', lat, lng);

                        // Remove existing marker
                        if (marker) {
                            map.removeLayer(marker);
                        }

                        // Add marker at real location
                        marker = L.marker([lat, lng], {icon: customIcon}).addTo(map);

                        // Only center map on real location when using "Use My Real Location" button
                        map.setView([lat, lng], 13);

                        // Get address for real location
                        fetch(\`https://nominatim.openstreetmap.org/reverse?format=json&lat=\${lat}&lon=\${lng}&zoom=18&addressdetails=1\`)
                            .then(response => response.json())
                            .then(data => {
                                let address = 'Your Real Location';
                                if (data && data.display_name) {
                                    address = data.display_name;
                                }

                                console.log('Sending location to React Native:', { lat, lng, address });

                                // Send location data to React Native
                                window.ReactNativeWebView.postMessage(JSON.stringify({
                                    type: 'locationSelected',
                                    lat: lat,
                                    lng: lng,
                                    address: address
                                }));
                            })
                            .catch(error => {
                                console.log('Reverse geocoding failed:', error);

                                // Send location data anyway
                                window.ReactNativeWebView.postMessage(JSON.stringify({
                                    type: 'locationSelected',
                                    lat: lat,
                                    lng: lng,
                                    address: 'Your Real Location'
                                }));
                            });

                        document.getElementById('resetButton').textContent = '📍 Use My Real Location';
                        document.getElementById('resetButton').disabled = false;
                    }, function(error) {
                        console.log('Geolocation error:', error.code, error.message);
                        let errorMessage = 'Could not get your location. ';

                        switch(error.code) {
                            case error.PERMISSION_DENIED:
                                errorMessage += 'Location access denied. Please enable location permissions.';
                                break;
                            case error.POSITION_UNAVAILABLE:
                                errorMessage += 'Location information unavailable.';
                                break;
                            case error.TIMEOUT:
                                errorMessage += 'Location request timed out.';
                                break;
                            default:
                                errorMessage += 'Unknown error occurred.';
                                break;
                        }

                        alert(errorMessage);
                        document.getElementById('resetButton').textContent = '📍 Use My Real Location';
                        document.getElementById('resetButton').disabled = false;
                    }, options);
                } else {
                    alert('Geolocation is not supported by this browser.');
                }
            }
        </script>
    </body>
    </html>
    `;
  };

  // Confirm coordinate location selection
  const confirmCoordinateLocation = async () => {
    if (!selectedMapLocation) {
      Alert.alert('Error', 'Please select a location on the map');
      return;
    }

    setIsLoadingPremium(true);
    try {
      let locationInfo = {
        city: selectedMapLocation.city || 'Custom Location',
        country: selectedMapLocation.country || 'Unknown'
      };

      // Try to get more detailed location info if we don't have it
      if (!selectedMapLocation.city || selectedMapLocation.city === 'Custom Location') {
        try {
          locationInfo = await reverseGeocodeLocation(
            selectedMapLocation.latitude,
            selectedMapLocation.longitude
          );
        } catch (error) {
          console.log('Using default location info');
        }
      }

      const success = await updateCustomLocationWithCoords(
        locationInfo.city,
        locationInfo.country,
        selectedMapLocation.latitude,
        selectedMapLocation.longitude
      );

      if (success) {
        setShowMapModal(false);
        setSelectedMapLocation(null);
      }
    } catch (error) {
      console.error('Error confirming coordinate location:', error);
      Alert.alert('Error', 'Failed to set location');
    } finally {
      setIsLoadingPremium(false);
    }
  };

  // Render blocked user item
  const renderBlockedUser = ({ item }) => {
    const userId = item;
    const userProfile = blockedUsernames[userId];
    const username = userProfile?.username || 'Unknown User';
    const profileImage = userProfile?.images && userProfile.images.length > 0 ? userProfile.images[0] : null;

    return (
      <View style={styles.blockedUserItem}>
        <View style={styles.blockedUserInfo}>
          <View style={styles.blockedUserAvatar}>
            {profileImage ? (
              <Image
                source={{
                  uri: profileImage.startsWith('data:')
                    ? profileImage
                    : `data:image/jpeg;base64,${profileImage}`
                }}
                style={styles.blockedUserAvatarImage}
                resizeMode="cover"
              />
            ) : (
              <Text style={styles.blockedUserAvatarText}>
                {username.charAt(0).toUpperCase()}
              </Text>
            )}
          </View>
          <View style={styles.blockedUserDetails}>
            <Text style={styles.blockedUserName}>{username}</Text>
            {userProfile?.age && (
              <Text style={styles.blockedUserAge}>Age {userProfile.age}</Text>
            )}
          </View>
        </View>
        <TouchableOpacity
          style={styles.unblockButton}
          onPress={() => handleUnblockUser(userId)}
        >
          <Text style={styles.unblockButtonText}>Unblock</Text>
        </TouchableOpacity>
      </View>
    );
  };

  // Get location status text and color
  const getLocationStatusInfo = () => {
    if (locationPermission === 'granted') {
      return {
        text: 'Location services enabled',
        color: '#4e9af1',
        icon: 'checkmark-circle'
      };
    } else if (locationPermission === 'denied') {
      return {
        text: 'Location services disabled',
        color: '#ff3b30',
        icon: 'alert-circle'
      };
    } else {
      return {
        text: 'Location permission not determined',
        color: '#ff9500',
        icon: 'help-circle'
      };
    }
  };

  const locationInfo = getLocationStatusInfo();

  return (
    <ScrollView style={styles.container}>
      {/* Location Services Section - Only show if location is not granted */}
      {locationPermission !== 'granted' && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Location Services</Text>

          <View style={styles.locationStatusContainer}>
            <Ionicons name={locationInfo.icon} size={24} color={locationInfo.color} style={styles.locationIcon} />
            <Text style={[styles.locationStatusText, { color: locationInfo.color }]}>
              {locationInfo.text}
            </Text>
          </View>

          <Text style={styles.locationDescription}>
            Shake & Match requires location services to find nearby matches. Without location access, the app cannot function properly.
          </Text>

          <TouchableOpacity
            style={[
              styles.locationButton,
              { backgroundColor: '#4e9af1' }
            ]}
            onPress={requestLocationPermission}
            disabled={isCheckingLocation}
          >
            {isCheckingLocation ? (
              <Text style={styles.locationButtonText}>Checking...</Text>
            ) : (
              <Text style={styles.locationButtonText}>Enable Location</Text>
            )}
          </TouchableOpacity>
        </View>
      )}

      {/* Premium Section */}
      <View style={[styles.section, (premiumStatus && premiumStatus.isPremium) ? styles.premiumSection : null]}>
        <View style={styles.premiumHeader}>
          <Text style={styles.sectionTitle}>Premium Features</Text>
          <View style={styles.premiumHeaderRight}>
            {(premiumStatus && premiumStatus.isPremium) && (
              <View style={styles.premiumBadge}>
                <Ionicons name="star" size={16} color="#ff9d00ff" />
                <Text style={styles.premiumBadgeText}>PREMIUM</Text>
              </View>
            )}
            <TouchableOpacity
              style={styles.refreshButton}
              onPress={loadPremiumStatus}
              disabled={isLoadingPremium}
            >
              <Ionicons
                name="refresh"
                size={20}
                color={isLoadingPremium ? "#ccc" : "#4e9af1"}
              />
            </TouchableOpacity>
          </View>
        </View>



        {(premiumStatus && premiumStatus.isPremium) ? (
          <>
            <View style={styles.premiumInfo}>
              <Text style={styles.premiumInfoText}>
                You have an active premium subscription!
              </Text>
              {(premiumStatus.daysRemaining && premiumStatus.daysRemaining > 0) && (
                <Text style={styles.premiumDaysText}>
                  {premiumStatus.daysRemaining} days remaining
                </Text>
              )}
            </View>

            {/* Custom Location Feature */}
            <View style={styles.premiumFeature}>
              <View style={styles.featureHeader}>
                <Ionicons name="location" size={20} color="#4e9af1" />
                <Text style={styles.featureTitle}>Custom Location</Text>
              </View>

              {(customLocation && customLocation.enabled) ? (
                <View style={styles.customLocationActive}>
                  <Text style={styles.customLocationText}>
                    Active: {customLocation?.city || 'Unknown'}, {customLocation?.country || 'Unknown'}
                  </Text>
                  <View style={styles.locationButtons}>
                    <TouchableOpacity
                      style={styles.changeLocationButton}
                      onPress={openCoordinateLocationPicker}
                    >
                      <Ionicons name="location" size={16} color="#fff" />
                      <Text style={styles.changeLocationButtonText}>Change Location</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={styles.disableLocationButton}
                      onPress={disableCustomLocation}
                      disabled={isLoadingPremium}
                    >
                      <Text style={styles.disableLocationButtonText}>
                        {isLoadingPremium ? 'Disabling...' : 'Disable'}
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              ) : (
                <View style={styles.customLocationInactive}>
                  <Text style={styles.featureDescription}>
                    Change your location to appear in different cities and match with users worldwide.
                  </Text>
                  <TouchableOpacity
                    style={styles.enableLocationButton}
                    onPress={openCoordinateLocationPicker}
                    disabled={isLoadingPremium}
                  >
                    <Ionicons name="location" size={20} color="#fff" />
                    <Text style={styles.enableLocationButtonText}>
                      {isLoadingPremium ? 'Loading...' : 'Set Custom Location'}
                    </Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>
          </>
        ) : (
          <View style={styles.premiumUpgrade}>
            <View style={styles.authWarning}>
              <Ionicons name="warning" size={24} color="#FF3B30" />
              <Text style={styles.authWarningText}>
                Authentication Required
              </Text>
            </View>
            <Text style={styles.authWarningDescription}>
              You need to log in again to check your premium status and access premium features.
            </Text>
            <TouchableOpacity
              style={[styles.upgradeButton, { backgroundColor: '#FF3B30' }]}
              onPress={() => navigation.navigate('Login')}
            >
              <Ionicons name="log-in" size={20} color="#fff" />
              <Text style={styles.upgradeButtonText}>Log In Again</Text>
            </TouchableOpacity>

            <View style={styles.premiumFeaturesList}>
              <Text style={styles.upgradeTitle}>Premium Features Available:</Text>
              <Text style={styles.upgradeDescription}>
                • Change your location to match with users worldwide{'\n'}
                • Priority matching{'\n'}
                • Advanced filters{'\n'}
                • And more!
              </Text>
            </View>
          </View>
        )}
      </View>


      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Match Settings</Text>
        
        <View style={styles.settingItem}>
          <Text style={styles.settingLabel}>Maximum Match Distance</Text>
          <Text style={styles.settingValue}>{Math.round(distance)} km</Text>
        </View>
        
        <Slider
          style={styles.slider}
          minimumValue={1}
          maximumValue={50}
          step={1}
          value={distance}
          onValueChange={handleDistanceChange}
          minimumTrackTintColor="#4e9af1"
          maximumTrackTintColor="#d3d3d3"
          thumbTintColor="#4e9af1"
        />
        
        <View style={styles.sliderLabels}>
          <Text style={styles.sliderLabel}>1 km</Text>
          <Text style={styles.sliderLabel}>50 km</Text>
        </View>
        
        <View style={styles.divider} />
        
        {/* Minimum Age Setting */}
        <View style={styles.settingItem}>
          <Text style={styles.settingLabel}>Minimum Age</Text>
          <Text style={styles.settingValue}>{minimumAge} years</Text>
        </View>
        
        <Slider
          style={styles.slider}
          minimumValue={18}
          maximumValue={75}
          step={1}
          value={minimumAge}
          onValueChange={handleMinAgeChange}
          minimumTrackTintColor="#4e9af1"
          maximumTrackTintColor="#d3d3d3"
          thumbTintColor="#4e9af1"
        />
        
        <View style={styles.sliderLabels}>
          <Text style={styles.sliderLabel}>18 yrs</Text>
          <Text style={styles.sliderLabel}>75 yrs</Text>
        </View>
        
        {/* Maximum Age Setting */}
        <View style={styles.settingItem}>
          <Text style={styles.settingLabel}>Maximum Age</Text>
          <Text style={styles.settingValue}>{maximumAge} years</Text>
        </View>
        
        <Slider
          style={styles.slider}
          minimumValue={19}
          maximumValue={99}
          step={1}
          value={maximumAge}
          onValueChange={handleMaxAgeChange}
          minimumTrackTintColor="#4e9af1"
          maximumTrackTintColor="#d3d3d3"
          thumbTintColor="#4e9af1"
        />
        
        <View style={styles.sliderLabels}>
          <Text style={styles.sliderLabel}>19 yrs</Text>
          <Text style={styles.sliderLabel}>99 yrs</Text>
        </View>
        
        <Text style={styles.settingDescription}>
          You will only be matched with users who are between {minimumAge} and {maximumAge} years old.
        </Text>
      </View>

      {/* Gender and Matching Preferences Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Profile & Matching</Text>

        {/* User Gender Selection */}
        <View style={styles.settingItem}>
          <Text style={styles.settingLabel}>My Gender</Text>
        </View>
        <View style={styles.genderContainer}>
          <TouchableOpacity
            style={[styles.genderButton, userGender === 'male' && styles.genderButtonSelected]}
            onPress={() => {
              setUserGender('male');
              saveUserPreferences('male', interestedIn, lookingFor);
            }}
          >
            <Ionicons
              name="man"
              size={20}
              color={userGender === 'male' ? '#fff' : '#4e9af1'}
            />
            <Text style={[styles.genderButtonText, userGender === 'male' && styles.genderButtonTextSelected]}>
              Male
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.genderButton, userGender === 'female' && styles.genderButtonSelected]}
            onPress={() => {
              setUserGender('female');
              saveUserPreferences('female', interestedIn, lookingFor);
            }}
          >
            <Ionicons
              name="woman"
              size={20}
              color={userGender === 'female' ? '#fff' : '#4e9af1'}
            />
            <Text style={[styles.genderButtonText, userGender === 'female' && styles.genderButtonTextSelected]}>
              Female
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.divider} />

        {/* Interested In Selection */}
        <View style={styles.settingItem}>
          <Text style={styles.settingLabel}>Interested In</Text>
        </View>
        <View style={styles.genderContainer}>
          <TouchableOpacity
            style={[styles.genderButton, interestedIn === 'male' && styles.genderButtonSelected]}
            onPress={() => {
              setInterestedIn('male');
              saveUserPreferences(userGender, 'male', lookingFor);
            }}
          >
            <Ionicons
              name="man"
              size={20}
              color={interestedIn === 'male' ? '#fff' : '#4e9af1'}
            />
            <Text style={[styles.genderButtonText, interestedIn === 'male' && styles.genderButtonTextSelected]}>
              Male
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.genderButton, interestedIn === 'female' && styles.genderButtonSelected]}
            onPress={() => {
              setInterestedIn('female');
              saveUserPreferences(userGender, 'female', lookingFor);
            }}
          >
            <Ionicons
              name="woman"
              size={20}
              color={interestedIn === 'female' ? '#fff' : '#4e9af1'}
            />
            <Text style={[styles.genderButtonText, interestedIn === 'female' && styles.genderButtonTextSelected]}>
              Female
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.genderButton, interestedIn === 'both' && styles.genderButtonSelected]}
            onPress={() => {
              setInterestedIn('both');
              saveUserPreferences(userGender, 'both', lookingFor);
            }}
          >
            <Ionicons
              name="people"
              size={20}
              color={interestedIn === 'both' ? '#fff' : '#4e9af1'}
            />
            <Text style={[styles.genderButtonText, interestedIn === 'both' && styles.genderButtonTextSelected]}>
              Both
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.divider} />

        {/* Looking For Toggle */}
        <View style={styles.settingToggle}>
          <View style={styles.toggleInfo}>
            <Text style={styles.settingLabel}>Looking For</Text>
            <Text style={styles.toggleDescription}>
              {lookingFor === 'relationship' ? 'Relationship' : 'Friends'}
            </Text>
          </View>
          <View style={styles.customToggle}>
            <TouchableOpacity
              style={[styles.toggleOption, lookingFor === 'relationship' && styles.toggleOptionActive]}
              onPress={() => {
                setLookingFor('relationship');
                saveUserPreferences(userGender, interestedIn, 'relationship');
              }}
            >
              <Ionicons
                name="heart"
                size={16}
                color={lookingFor === 'relationship' ? '#fff' : '#4e9af1'}
              />
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.toggleOption, lookingFor === 'friends' && styles.toggleOptionActive]}
              onPress={() => {
                setLookingFor('friends');
                saveUserPreferences(userGender, interestedIn, 'friends');
              }}
            >
              <Ionicons
                name="people"
                size={16}
                color={lookingFor === 'friends' ? '#fff' : '#4e9af1'}
              />
            </TouchableOpacity>
          </View>
        </View>
      </View>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Notifications</Text>
        
        <View style={styles.settingToggle}>
          <Text style={styles.settingLabel}>Enable Notifications</Text>
          <Switch
            value={notificationsEnabled}
            onValueChange={setNotificationsEnabled}
            trackColor={{ false: '#d3d3d3', true: '#4e9af1' }}
            thumbColor="#fff"
          />
        </View>
      </View>
      
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Blocked Users</Text>
        
        {blockedUsers.length === 0 ? (
          <Text style={styles.noBlockedText}>You haven't blocked any users.</Text>
        ) : (
          <FlatList
            data={blockedUsers}
            keyExtractor={(item) => item}
            renderItem={renderBlockedUser}
            scrollEnabled={false}
          />
        )}
      </View>
      
      <View style={styles.section}>
        <TouchableOpacity 
          style={styles.logoutButton}
          onPress={handleLogout}
        >
          <Ionicons name="log-out-outline" size={24} color="#fff" />
          <Text style={styles.logoutButtonText}>Logout</Text>
        </TouchableOpacity>
      </View>

      {/* Custom Location Modal */}
      <Modal
        visible={showLocationModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowLocationModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setShowLocationModal(false)}
            >
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Set Custom Location</Text>
            <View style={styles.modalHeaderSpacer} />
          </View>

          <View style={styles.modalContent}>
            <Text style={styles.modalDescription}>
              Choose a city where you'd like to appear for matching. This is a premium feature that lets you connect with users worldwide.
            </Text>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>City</Text>
              <TextInput
                style={styles.textInput}
                value={newCity}
                onChangeText={setNewCity}
                placeholder="Enter city name"
                placeholderTextColor="#999"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Country</Text>
              <TextInput
                style={styles.textInput}
                value={newCountry}
                onChangeText={setNewCountry}
                placeholder="Enter country name"
                placeholderTextColor="#999"
              />
            </View>

            <TouchableOpacity
              style={[styles.saveLocationButton, { opacity: isLoadingPremium ? 0.6 : 1 }]}
              onPress={updateCustomLocation}
              disabled={isLoadingPremium}
            >
              <Text style={styles.saveLocationButtonText}>
                {isLoadingPremium ? 'Saving...' : 'Save Location'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Interactive Map Modal */}
      <Modal
        visible={showMapModal}
        animationType="slide"
        presentationStyle="fullScreen"
        onRequestClose={() => setShowMapModal(false)}
      >
        <View style={styles.mapModalContainer}>
          <View style={styles.mapModalHeader}>
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setShowMapModal(false)}
            >
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Choose Your Location</Text>
            <TouchableOpacity
              style={[styles.confirmMapButton, { opacity: selectedMapLocation ? 1 : 0.5 }]}
              onPress={confirmCoordinateLocation}
              disabled={!selectedMapLocation || isLoadingPremium}
            >
              <Text style={styles.confirmMapButtonText}>
                {isLoadingPremium ? 'Setting...' : 'Confirm'}
              </Text>
            </TouchableOpacity>
          </View>

          <View style={styles.interactiveMapContainer}>
            <WebView
              style={styles.mapWebView}
              source={{ html: getGoogleMapsHTML() }}
              onMessage={handleMapMessage}
              javaScriptEnabled={true}
              domStorageEnabled={true}
              startInLoadingState={true}
              renderLoading={() => (
                <View style={styles.mapLoadingContainer}>
                  <Text style={styles.mapLoadingText}>Loading Google Maps...</Text>
                </View>
              )}
            />
          </View>

          <View style={styles.mapFooter}>
            {selectedMapLocation ? (
              <Text style={styles.selectedLocationText}>
                📍 Selected: {selectedMapLocation.city || 'Custom Location'}
                {'\n'}Coordinates: {selectedMapLocation.latitude.toFixed(4)}, {selectedMapLocation.longitude.toFixed(4)}
              </Text>
            ) : (
              <Text style={styles.mapInstructionsText}>
                Tap on a continent or anywhere on the map to select your location
              </Text>
            )}
          </View>
        </View>
      </Modal>


    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginHorizontal: 15,
    marginTop: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 15,
  },
  // Location services styles (NEW)
  locationStatusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
    backgroundColor: '#f8f8f8',
    padding: 12,
    borderRadius: 8,
  },
  locationIcon: {
    marginRight: 10,
  },
  locationStatusText: {
    fontSize: 16,
    fontWeight: '500',
  },
  locationDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 15,
    lineHeight: 20,
  },
  locationButton: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 5,
  },
  locationButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  // Original styles
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  settingLabel: {
    fontSize: 16,
    color: '#444',
  },
  settingValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#4e9af1',
  },
  settingDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 10,
    fontStyle: 'italic',
  },
  slider: {
    width: '100%',
    height: 40,
  },
  sliderLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: -10,
  },
  sliderLabel: {
    fontSize: 14,
    color: '#999',
  },
  settingToggle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  divider: {
    height: 1,
    backgroundColor: '#eee',
    marginVertical: 15,
  },
  updateButton: {
    backgroundColor: '#4e9af1',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 8,
  },
  updateButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
  serverNote: {
    fontSize: 14,
    color: '#888',
    marginTop: 10,
    fontStyle: 'italic',
  },
  noBlockedText: {
    fontSize: 16,
    color: '#888',
    textAlign: 'center',
    paddingVertical: 15,
  },
  blockedUserItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  blockedUserInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  blockedUserAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f85149',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
    overflow: 'hidden',
  },
  blockedUserAvatarImage: {
    width: '100%',
    height: '100%',
  },
  blockedUserAvatarText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  blockedUserDetails: {
    flex: 1,
  },
  blockedUserName: {
    fontSize: 16,
    color: '#333',
    fontWeight: '600',
  },
  blockedUserAge: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  unblockButton: {
    backgroundColor: '#f0f0f0',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 6,
  },
  unblockButtonText: {
    color: '#f85149',
    fontSize: 14,
    fontWeight: '500',
  },
  logoutButton: {
    backgroundColor: '#f85149',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 15,
    borderRadius: 10,
  },
  logoutButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  // Gender and preference styles
  genderContainer: {
    flexDirection: 'row',
    gap: 10,
    marginBottom: 15,
  },
  genderButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#4e9af1',
    backgroundColor: '#fff',
    gap: 8,
  },
  genderButtonSelected: {
    backgroundColor: '#4e9af1',
    borderColor: '#4e9af1',
  },
  genderButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#4e9af1',
  },
  genderButtonTextSelected: {
    color: '#fff',
  },
  toggleInfo: {
    flex: 1,
  },
  toggleDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  customToggle: {
    flexDirection: 'row',
    backgroundColor: '#f0f0f0',
    borderRadius: 20,
    padding: 2,
  },
  toggleOption: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 18,
    backgroundColor: 'transparent',
    alignItems: 'center',
    justifyContent: 'center',
  },
  toggleOptionActive: {
    backgroundColor: '#4e9af1',
  },
  // Premium styles
  premiumHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  premiumHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  refreshButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(78, 154, 241, 0.1)',
  },
  // Authentication warning styles
  authWarning: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 10,
    gap: 8,
  },
  authWarningText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FF3B30',
  },
  authWarningDescription: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 20,
  },
  premiumSection: {
    backgroundColor: '#fff9e6',
    borderColor: '#ffd700',
    borderWidth: 2,
    shadowColor: '#ffd700',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  premiumFeaturesList: {
    marginTop: 20,
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  premiumBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFD700',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  premiumBadgeText: {
    color: '#000',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  premiumInfo: {
    backgroundColor: '#fff9e6',
    padding: 15,
    borderRadius: 8,
    marginBottom: 15,
    borderWidth: 1,
    borderColor: '#ffd700',
  },
  premiumInfoText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#b8860b',
    marginBottom: 5,
  },
  premiumDaysText: {
    fontSize: 14,
    color: '#666',
  },
  premiumFeature: {
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    paddingTop: 15,
  },
  featureHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
  featureDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 15,
    lineHeight: 20,
  },
  customLocationActive: {
    backgroundColor: '#e8f5e8',
    padding: 15,
    borderRadius: 8,
  },
  customLocationText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2d5a2d',
    marginBottom: 10,
  },
  locationButtons: {
    flexDirection: 'row',
    gap: 10,
  },
  changeLocationButton: {
    backgroundColor: '#4e9af1',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 8,
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 6,
  },
  changeLocationButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  disableLocationButton: {
    backgroundColor: '#ff6b6b',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 8,
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  disableLocationButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  customLocationInactive: {
    padding: 15,
  },
  enableLocationButton: {
    backgroundColor: '#4e9af1',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  enableLocationButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  premiumUpgrade: {
    padding: 20,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    alignItems: 'center',
  },
  upgradeTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 10,
    textAlign: 'center',
  },
  upgradeDescription: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 20,
  },
  upgradeButton: {
    backgroundColor: '#FFD700',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  upgradeButtonText: {
    color: '#000',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  // Modal styles
  modalContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalCloseButton: {
    padding: 5,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalHeaderSpacer: {
    width: 34, // Same width as close button for centering
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  modalDescription: {
    fontSize: 16,
    color: '#666',
    marginBottom: 30,
    lineHeight: 22,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
    color: '#333',
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 15,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  saveLocationButton: {
    backgroundColor: '#4e9af1',
    paddingVertical: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 20,
  },
  saveLocationButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  // Map-related styles
  locationSetupButtons: {
    flexDirection: 'row',
    gap: 10,
  },
  mapLocationButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  textLocationButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  mapModalContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  mapModalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    paddingTop: 50, // Account for status bar
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    backgroundColor: '#fff',
  },
  confirmMapButton: {
    backgroundColor: '#4e9af1',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  confirmMapButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  // Interactive Map Styles
  interactiveMapContainer: {
    flex: 1,
    backgroundColor: '#f0f0f0',
  },
  mapWebView: {
    flex: 1,
    borderRadius: 12,
    overflow: 'hidden',
  },
  mapLoadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
  },
  mapLoadingText: {
    fontSize: 16,
    color: '#666',
    marginTop: 10,
  },
  mapFooter: {
    backgroundColor: '#f8f9fa',
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  selectedLocationText: {
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
    fontWeight: '500',
    lineHeight: 22,
  },
  mapInstructionsText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
  },

});

export default SettingsScreen;