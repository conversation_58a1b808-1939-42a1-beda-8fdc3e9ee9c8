// App.js - With Login as first screen and fixed navigation
import React, { useState, useEffect, useRef } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { Accelerometer } from 'expo-sensors';
import * as Location from 'expo-location';
import { io } from 'socket.io-client';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { StatusBar, View, Alert, Linking, Platform } from 'react-native';
import 'react-native-gesture-handler';

// Import screens
import LoginScreen from './screens/LoginScreen';
import HomeScreen from './screens/HomeScreen';
import SettingsScreen from './screens/SettingsScreen';
import ChatScreen from './screens/ChatScreen';
import MatchScreen from './screens/MatchScreen';
import ViewProfileScreen from './screens/ViewProfileScreen';
import ProfileScreen from './screens/ProfileScreen';

const Stack = createStackNavigator();

export default function App() {
  // State variables and refs remain the same
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [user, setUser] = useState(null);
  const [shakeDetected, setShakeDetected] = useState(false);
  const [location, setLocation] = useState(null);
  const [maxDistance, setMaxDistance] = useState(5); // Default 5km
  const [matches, setMatches] = useState([]);
  const [blockedUsers, setBlockedUsers] = useState([]);
  const socketRef = useRef(null);
  const [serverAddress, setServerAddress] = useState('');
  const navigationRef = useRef(null);
  const inactivityCheckIntervalRef = useRef(null);
  const [userProfile, setUserProfile] = useState(null);
  const [minAge, setMinAge] = useState(18);  // Default min age
  const [maxAge, setMaxAge] = useState(100);  // Default max age

  // Check for stored credentials on app start with database validation
  useEffect(() => {
    const checkLoginStatus = async () => {
      try {
        const userJSON = await AsyncStorage.getItem('user');
        const storedServer = await AsyncStorage.getItem('serverAddress');
        const storedMaxDistance = await AsyncStorage.getItem('maxDistance');
        const storedMinAge = await AsyncStorage.getItem('minAge');
        const storedMaxAge = await AsyncStorage.getItem('maxAge');

        let loginSuccessful = false;

        if (userJSON && storedServer) {
          console.log('Found stored user data:', userJSON);
          
          try {
            const userData = JSON.parse(userJSON);
            
            // Verify the user data has a valid ID
            if (!userData.id) {
              console.error('Stored user data missing ID:', userData);
              // Don't log in with invalid data
              await AsyncStorage.removeItem('user');
            } else {
              // Validate that the user still exists in the database
              try {
                const response = await fetch(`http://${storedServer}/api/debug/user/${userData.id}`, {
                  method: 'GET',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                });
                
                const data = await response.json();
                
                if (!response.ok || !data.exists) {
                  // User doesn't exist
                  console.log('User no longer exists in database, clearing credentials');
                  await AsyncStorage.removeItem('user');
                } else {
                  // User exists, proceed with login
                  console.log('User validation successful');
                  console.log('Logging in with stored credentials:', userData.username, userData.id);
                  setUser(userData);
                  setIsLoggedIn(true);
                  setServerAddress(storedServer);
                  loginSuccessful = true;
                }
              } catch (validationError) {
                console.error('Error validating user:', validationError);
                // On error, be cautious and clear credentials
                await AsyncStorage.removeItem('user');
              }
            }
          } catch (parseError) {
            console.error('Error parsing stored user data:', parseError);
            await AsyncStorage.removeItem('user');
          }
        } else {
          console.log('No stored user data found or missing server address');
        }
        
        if (storedServer) {
          setServerAddress(storedServer);
        }
        
        if (storedMaxDistance) {
          setMaxDistance(parseFloat(storedMaxDistance) || 5);
        }
        
        if (storedMinAge) {
          setMinAge(parseInt(storedMinAge) || 18);
        }
        
        if (storedMaxAge) {
          setMaxAge(parseInt(storedMaxAge) || 100);
        }
        
        // Ensure isLoggedIn is false if login wasn't successful
        if (!loginSuccessful) {
          setIsLoggedIn(false);
          setUser(null);
        }
      } catch (error) {
        console.error('Error checking login status:', error);
        setIsLoggedIn(false);
        setUser(null);
      }
    };
    
    checkLoginStatus();
  }, []);

  //handle updating age range settings
  const updateAgeRange = async (min, max) => {
    setMinAge(min);
    setMaxAge(max);
    try {
      await AsyncStorage.setItem('minAge', min.toString());
      await AsyncStorage.setItem('maxAge', max.toString());
    } catch (error) {
      console.error('Error saving age range settings:', error);
    }
  };

  // Load stored matches and blocked users
  useEffect(() => {
    const loadUserData = async () => {
      try {
        if (user?.id) {
          // Load matches
          const storedMatches = await AsyncStorage.getItem(`matches_${user.id}`);
          if (storedMatches) {
            setMatches(JSON.parse(storedMatches));
          }
          
          // Load blocked users
          const storedBlockedUsers = await AsyncStorage.getItem(`blocked_${user.id}`);
          if (storedBlockedUsers) {
            setBlockedUsers(JSON.parse(storedBlockedUsers));
          }
          // Loading user profile
          const storedProfile = await AsyncStorage.getItem(`profile_${user.id}`);
          if (storedProfile) {
            setUserProfile(JSON.parse(storedProfile));
          }
        }
      } catch (error) {
        console.error('Error loading user data:', error);
      }
    };
    
    if (isLoggedIn && user) {
      loadUserData();
    }
  }, [isLoggedIn, user]);

  useEffect(() => {
    const checkProfileCompletion = async () => {
      if (isLoggedIn && user?.id) {
        try {
          // Get user profile
          const storedProfile = await AsyncStorage.getItem(`profile_${user.id}`);
          let profileData = null;
          
          if (storedProfile) {
            profileData = JSON.parse(storedProfile);
          }
          
          // Check if profile is complete
          const isProfileComplete = 
            profileData && 
            profileData.description && 
            profileData.age && 
            profileData.images && 
            profileData.images.length >= 3;
          
          // If profile is incomplete, force navigation to profile screen
          if (!isProfileComplete && navigationRef.current) {
            console.log('Profile incomplete, redirecting to profile setup');
            // Use a timeout to ensure navigation is ready
            setTimeout(() => {
              navigationRef.current.reset({
                index: 0,
                routes: [{ 
                  name: 'ProfileScreen', 
                  params: { isInitialSetup: true }
                }]
              });
            }, 500);
          }
        } catch (error) {
          console.error('Error checking profile completion:', error);
        }
      }
    };
    
    checkProfileCompletion();
  }, [isLoggedIn, user]);

  const handleUpdateProfile = (profileData) => {
    setUserProfile(profileData);
  };
  
  // Helper to check if a user is blocked
  const isUserBlocked = (userId) => {
    return blockedUsers.includes(userId);
  };

  // Helper to check if the user is currently in a chat with someone
  const isUserInChatWith = (userId) => {
    if (!navigationRef.current) return false;
    
    try {
      const currentRoute = navigationRef.current.getCurrentRoute();
      if (currentRoute?.name === 'Chat' && currentRoute?.params?.match?.userId === userId) {
        return true;
      }
    } catch (e) {
      // If there's any error reading the route, assume user is not in chat
      console.error('Error checking current route:', e);
    }
    
    return false;
  };

  // Function to block a user
  const handleBlockUser = async (userId) => {
    try {
      if (!user?.id || !userId) {
        console.error('Cannot block user: Missing user ID or target ID');
        return;
      }
      
      // First, check if already blocked
      if (isUserBlocked(userId)) {
        return; // Already blocked
      }
      
      // Add to blocked users list
      const updatedBlockedUsers = [...blockedUsers, userId];
      setBlockedUsers(updatedBlockedUsers);
      
      // Save blocked users to AsyncStorage
      await AsyncStorage.setItem(`blocked_${user.id}`, JSON.stringify(updatedBlockedUsers));
      
      // Delete any existing chat with this user
      await handleDeleteChat(userId, true);
      
      // Notify server of block
      if (socketRef.current && socketRef.current.connected) {
        socketRef.current.emit('blockUser', {
          userId: user.id,
          blockedUserId: userId
        });
      }
      
      console.log('User blocked successfully');
    } catch (error) {
      console.error('Error blocking user:', error);
    }
  };

  // Function to unblock a user
  const handleUnblockUser = async (userId) => {
    try {
      if (!user?.id || !userId) {
        console.error('Cannot unblock user: Missing user ID or target ID');
        return;
      }
      
      // Remove from blocked users list
      const updatedBlockedUsers = blockedUsers.filter(id => id !== userId);
      setBlockedUsers(updatedBlockedUsers);
      
      // Save updated blocked users to AsyncStorage
      await AsyncStorage.setItem(`blocked_${user.id}`, JSON.stringify(updatedBlockedUsers));
      
      // Notify server of unblock
      if (socketRef.current && socketRef.current.connected) {
        socketRef.current.emit('unblockUser', {
          userId: user.id,
          unblockedUserId: userId
        });
      }
      
      console.log('User unblocked successfully');
    } catch (error) {
      console.error('Error unblocking user:', error);
    }
  };

  // Function to save messages to AsyncStorage
  const saveMessageToStorage = async (messageData) => {
    try {
      if (!messageData || !messageData.senderId || !messageData.receiverId) {
        console.error('Invalid message data for storage');
        return;
      }
      
      // Create a unique chat ID by sorting user IDs
      const chatId = [messageData.senderId, messageData.receiverId].sort().join('_');
      
      // Get existing messages for this chat
      const storedMessagesJSON = await AsyncStorage.getItem(`chat_${chatId}`);
      let storedMessages = [];
      
      if (storedMessagesJSON) {
        storedMessages = JSON.parse(storedMessagesJSON);
      }
      
      // Format the message to match the structure used in ChatScreen
      const formattedMessage = {
        id: messageData.id || Date.now().toString(),
        text: messageData.text,
        senderId: messageData.senderId,
        timestamp: messageData.timestamp || new Date().toISOString()
      };
      
      // Check if message already exists
      const messageExists = storedMessages.some(msg => msg.id === formattedMessage.id);
      if (messageExists) {
        return;
      }
      
      // Add the new message to the existing messages
      const updatedMessages = [...storedMessages, formattedMessage];
      
      // Save updated messages back to AsyncStorage
      await AsyncStorage.setItem(`chat_${chatId}`, JSON.stringify(updatedMessages));
      
      // Update the match's lastActivity timestamp
      updateMatchLastActivity(messageData.senderId === user.id ? messageData.receiverId : messageData.senderId);
      
      console.log('Message saved to storage successfully');
    } catch (error) {
      console.error('Error saving message to storage:', error);
    }
  };

  // Function to update a match's last activity timestamp
  const updateMatchLastActivity = (matchUserId) => {
    setMatches(prevMatches => {
      const updatedMatches = prevMatches.map(match => {
        if (match.userId === matchUserId) {
          return {
            ...match,
            lastActivity: new Date().toISOString(),
            hasActivity: true
          };
        }
        return match;
      });
      
      // Save the updated matches to AsyncStorage
      try {
        if (user?.id) {
          AsyncStorage.setItem(`matches_${user.id}`, JSON.stringify(updatedMatches));
        }
      } catch (e) {
        console.error('Error saving updated matches:', e);
      }
      
      return updatedMatches;
    });
  };

  // Function to handle unmatching (without blocking)
  const handleUnmatch = async (matchUserId) => {
    try {
      if (!user?.id || !matchUserId) {
        console.error('Cannot unmatch: Missing user ID or match ID');
        return;
      }

      // 1. First, delete the chat messages
      const chatId = [user.id, matchUserId].sort().join('_');
      await AsyncStorage.removeItem(`chat_${chatId}`);

      // 2. Then, update the matches array to remove the deleted match
      const updatedMatches = matches.filter(match => match.userId !== matchUserId);
      setMatches(updatedMatches);

      // 3. Save the updated matches to AsyncStorage
      await AsyncStorage.setItem(`matches_${user.id}`, JSON.stringify(updatedMatches));

      // 4. Allow rematch for unmatching (not blocking)
      if (socketRef.current && socketRef.current.connected) {
        // Send explicit allow rematch event
        socketRef.current.emit('allowRematch', {
          userId: user.id,
          otherUserId: matchUserId
        });

        // Send the chatDeleted event with allowRematch = true
        socketRef.current.emit('chatDeleted', {
          userId: user.id,
          otherUserId: matchUserId,
          reason: 'unmatched',
          allowRematch: true // Always allow rematch for unmatch
        });
      }

      console.log('Unmatched successfully. Rematch allowed');
    } catch (error) {
      console.error('Error unmatching:', error);
    }
  };

  // Function to handle deleting a chat
  const handleDeleteChat = async (matchUserId, isBlocking = false) => {
    try {
      if (!user?.id || !matchUserId) {
        console.error('Cannot delete chat: Missing user ID or match ID');
        return;
      }

      // 1. First, delete the chat messages
      const chatId = [user.id, matchUserId].sort().join('_');
      await AsyncStorage.removeItem(`chat_${chatId}`);

      // 2. Then, update the matches array to remove the deleted match
      const updatedMatches = matches.filter(match => match.userId !== matchUserId);
      setMatches(updatedMatches);

      // 3. Save the updated matches to AsyncStorage
      await AsyncStorage.setItem(`matches_${user.id}`, JSON.stringify(updatedMatches));

      // 4. CRITICAL: First, explicitly send an allowRematch event regardless of reason
      if (socketRef.current && socketRef.current.connected) {
        // Only if NOT blocking, send the explicit allow rematch event
        if (!isBlocking) {
          socketRef.current.emit('allowRematch', {
            userId: user.id,
            otherUserId: matchUserId
          });
        }

        // Then send the chatDeleted event with the appropriate allowRematch flag
        socketRef.current.emit('chatDeleted', {
          userId: user.id,
          otherUserId: matchUserId,
          reason: isBlocking ? 'blocked' : 'deleted',
          allowRematch: !isBlocking // Allow rematch if NOT blocking
        });
      }

      console.log(`Chat deleted successfully. Rematch ${isBlocking ? 'not allowed' : 'allowed'}`);
    } catch (error) {
      console.error('Error deleting chat:', error);
    }
  };

  // Set up interval to check for inactive matches
  useEffect(() => {
    if (isLoggedIn && user) {
      // Clear any existing interval
      if (inactivityCheckIntervalRef.current) {
        clearInterval(inactivityCheckIntervalRef.current);
      }
      
      // Set up a new interval to check for inactive matches
      inactivityCheckIntervalRef.current = setInterval(async () => {
        try {
          // Check each match for inactivity
          setMatches(prevMatches => {
            const now = new Date();
            const updatedMatches = prevMatches.filter(match => {
              // Skip matches that already have activity
              if (match.hasActivity) return true;
              
              // Check if match was created more than 1 minute ago
              const matchCreatedAt = new Date(match.createdAt || now);
              const inactiveThreshold = 60 * 1000; // 1 minute in milliseconds
              const isInactive = (now - matchCreatedAt) > inactiveThreshold;
              
              // If inactive and no message activity, delete it
              if (isInactive) {
                // Delete chat messages
                const chatId = [user.id, match.userId].sort().join('_');
                AsyncStorage.removeItem(`chat_${chatId}`);
                
                // Notify server that the chat was auto-deleted BUT ALLOW REMATCH
                if (socketRef.current && socketRef.current.connected) {
                  // Send explicit allow rematch event first
                  socketRef.current.emit('allowRematch', {
                    userId: user.id,
                    otherUserId: match.userId
                  });
                  
                  // Then send chat deleted with allowRematch flag
                  socketRef.current.emit('chatDeleted', {
                    userId: user.id,
                    otherUserId: match.userId,
                    reason: 'inactive',
                    allowRematch: true
                  });
                }
                
                return false; // Remove this match
              }
              
              return true; // Keep the match
            });
            
            // If matches changed, save them
            if (updatedMatches.length !== prevMatches.length) {
              AsyncStorage.setItem(`matches_${user.id}`, JSON.stringify(updatedMatches));
            }
            
            return updatedMatches;
          });
        } catch (error) {
          console.error('Error checking for inactive matches:', error);
        }
      }, 30000); // Check every 30 seconds
      
      // Clean up interval on unmount
      return () => {
        if (inactivityCheckIntervalRef.current) {
          clearInterval(inactivityCheckIntervalRef.current);
        }
      };
    }
  }, [isLoggedIn, user]);

  // Connect to socket when user logs in
  useEffect(() => {
    if (isLoggedIn && user && serverAddress) {
      // Connect to the user's self-hosted server
      socketRef.current = io(`http://${serverAddress}`);
      
      socketRef.current.on('connect', () => {
        console.log('Connected to server');
        // Register user with server
        socketRef.current.emit('register', {
          userId: user.id,
          username: user.username
        });
        
        // Send blocked users list to server
        socketRef.current.emit('syncBlocked', {
          userId: user.id,
          blockedUsers: blockedUsers
        });
        
        // NEW: Request server to clear any lingering match restrictions
        socketRef.current.emit('resetMatchRestrictions', {
          userId: user.id
        });
      });
      
      socketRef.current.on('match', (matchData) => {
        console.log('New match received:', matchData);
        
        // Ensure match has all required fields
        if (!matchData || !matchData.userId) {
          console.error('Received invalid match data:', matchData);
          return;
        }
        
        // Check if user is blocked
        if (isUserBlocked(matchData.userId)) {
          console.log('Ignoring match with blocked user:', matchData.userId);
          return;
        }
        
        setMatches(prev => {
          // Check if match already exists
          const matchExists = prev.some(m => m.userId === matchData.userId);
          if (matchExists) {
            return prev;
          }
          
          // Add createdAt and hasActivity fields to track engagement
          const updatedMatch = {
            ...matchData,
            createdAt: new Date().toISOString(),
            hasActivity: false
          };
          
          const updatedMatches = [...prev, updatedMatch];
          
          // Save matches to AsyncStorage
          try {
            AsyncStorage.setItem(`matches_${user.id}`, JSON.stringify(updatedMatches));
          } catch (e) {
            console.error('Error saving matches:', e);
          }
          
          return updatedMatches;
        });
      });
      
// Handle server-initiated navigation
socketRef.current.on('navigate', ({ screen, params }) => {
  console.log(`Navigation requested to ${screen}:`, params);
  
  // Add this check to prevent Match screen navigation
  if (screen === 'Match') {
    console.log('Match screen navigation prevented');
    return; // Skip navigation to Match screen
  }
  
  if (navigationRef.current && screen) {
    try {
      // Delay navigation slightly to ensure match data is stored
      setTimeout(() => {
        navigationRef.current.navigate(screen, params);
      }, 300);
    } catch (error) {
      console.error('Navigation error:', error);
    }
  } else {
    console.error('Cannot navigate: Navigation ref or screen name missing');
  }
});
      
      socketRef.current.on('message', (messageData) => {
        console.log('New message received:', messageData);
        
        // Validate message data
        if (!messageData || !messageData.senderId || !messageData.receiverId) {
          console.error('Received invalid message data:', messageData);
          return;
        }
        
        // Check if message is from a blocked user
        if (isUserBlocked(messageData.senderId)) {
          console.log('Ignoring message from blocked user:', messageData.senderId);
          return;
        }
        
        // Save the message to AsyncStorage regardless of whether user is in chat
        saveMessageToStorage(messageData);
        
        // Only process further if message is coming TO the current user
        // and not FROM the current user
        if (messageData.receiverId === user.id && messageData.senderId !== user.id) {
          setMatches(prevMatches => {
            // Find the match for this message
            const matchIndex = prevMatches.findIndex(m => m.userId === messageData.senderId);
            
            let updatedMatches = [...prevMatches];
            
            if (matchIndex === -1) {
              // This is a message from someone not in our matches list
              // We should recreate the match if it was deleted (and not blocked)
              console.log(`Recreating match with user ${messageData.senderUsername} after receiving a message`);
              
              // Create a new match object
              const newMatch = {
                userId: messageData.senderId,
                username: messageData.senderUsername,
                createdAt: new Date().toISOString(),
                hasActivity: true,
                lastActivity: new Date().toISOString(),
                hasUnreadMessages: true
              };
              
              // Add it to matches array
              updatedMatches = [...prevMatches, newMatch];
            } else {
              // Update existing match
              const isInChatWithSender = isUserInChatWith(messageData.senderId);
              
              // Create updated match object with activity flag set
              const updatedMatch = {
                ...prevMatches[matchIndex],
                hasActivity: true,
                lastActivity: new Date().toISOString()
              };
              
              // If user is not in chat with this sender, mark as unread
              if (!isInChatWithSender) {
                updatedMatch.hasUnreadMessages = true;
              }
              
              updatedMatches[matchIndex] = updatedMatch;
            }
            
            // Save updated matches
            try {
              AsyncStorage.setItem(`matches_${user.id}`, JSON.stringify(updatedMatches));
            } catch (e) {
              console.error('Error saving updated matches:', e);
            }
            
            return updatedMatches;
          });
        }
      });
      
      // Handle when unread status is cleared from ChatScreen
      socketRef.current.on('messagesRead', (data) => {
        if (data.userId === user.id) {
          setMatches(prevMatches => {
            const matchIndex = prevMatches.findIndex(m => m.userId === data.otherUserId);
            if (matchIndex === -1) return prevMatches;
            
            const updatedMatches = [...prevMatches];
            updatedMatches[matchIndex] = {
              ...updatedMatches[matchIndex],
              hasUnreadMessages: false,
              hasActivity: true,
              lastActivity: new Date().toISOString()
            };
            
            // Save updated matches
            try {
              AsyncStorage.setItem(`matches_${user.id}`, JSON.stringify(updatedMatches));
            } catch (e) {
              console.error('Error saving updated matches:', e);
            }
            
            return updatedMatches;
          });
        }
      });
      
      socketRef.current.on('userBlockedYou', (data) => {
        if (data.blockedUserId === user.id) {
          // Find the match info for this user
          const blockerMatch = matches.find(m => m.userId === data.userId);
          if (blockerMatch) {
            const blockerUsername = blockerMatch.username || 'Someone';
            
            // Remove this match from the list since they blocked the user
            handleDeleteChat(data.userId, false);
            
            // Show a notification if not currently in chat with this user
            if (!isUserInChatWith(data.userId)) {
              Alert.alert(
                "Blocked",
                `${blockerUsername} has blocked you.`,
                [{ text: "OK" }]
              );
            }
          }
        }
      });

      // Handle disconnect
      socketRef.current.on('disconnect', () => {
        console.log('Disconnected from server');
      });
      
      // Handle errors
      socketRef.current.on('error', (error) => {
        console.error('Socket error:', error);
      });
      
      return () => {
        if (socketRef.current) {
          socketRef.current.disconnect();
        }
      };
    }
  }, [isLoggedIn, user, serverAddress, blockedUsers]);

  // Set up accelerometer for shake detection with improved sensitivity
  useEffect(() => {
    let subscription;
    let lastShakeTime = 0;
    let shakeCount = 0;
    
    const startAccelerometer = async () => {
      try {
        const isAvailable = await Accelerometer.isAvailableAsync();
        
        if (!isAvailable) {
          console.log("Accelerometer not available on this device");
          return;
        }
        
        // Set update interval to 100ms for better shake detection
        Accelerometer.setUpdateInterval(100);
        
        subscription = Accelerometer.addListener(accelerometerData => {
          const { x, y, z } = accelerometerData;
          const acceleration = Math.sqrt(x*x + y*y + z*z);
          const now = Date.now();
          
          // Check if acceleration exceeds shake threshold
          if (acceleration > 1.8) { // Lower threshold for more sensitivity
            shakeCount++;
            
            // If we've detected 2+ shakes in quick succession
            if (shakeCount >= 2 && (now - lastShakeTime) < 1000) {
              console.log("Physical shake detected:", acceleration);
              // Reset shake count and time
              shakeCount = 0;
              lastShakeTime = now;
              
              // Call the same handleShake function
              handleShake();
            } else if ((now - lastShakeTime) > 1000) {
              // If it's been more than 1 second, reset count but record time
              shakeCount = 1;
              lastShakeTime = now;
            }
          }
        });
        
        console.log("Accelerometer listener set up successfully");
      } catch (error) {
        console.error("Error setting up accelerometer:", error);
      }
    };
    
    if (isLoggedIn) {
      console.log("Starting accelerometer setup");
      startAccelerometer();
    }
    
    return () => {
      if (subscription) {
        console.log("Removing accelerometer listener");
        subscription.remove();
      }
    };
  }, [isLoggedIn]);

  // Set up location services with enhanced error handling
  const setupLocation = async () => {
    try {
      console.log("Starting location setup");
      
      // First check if location services are enabled at the device level
      const providerStatus = await Location.getProviderStatusAsync();
      if (!providerStatus.locationServicesEnabled) {
        console.error("Location services are disabled at device level");
        Alert.alert(
          "Location Services Disabled",
          "Location services are turned off on your device. Please enable them in your device settings.",
          [
            { text: "OK" },
            { 
              text: "Open Settings", 
              onPress: () => {
                if (Platform.OS === 'ios') {
                  Linking.openURL('app-settings:');
                } else {
                  Linking.openSettings();
                }
              }
            }
          ]
        );
        return;
      }
      
      // Check if app has location permission
      const { status } = await Location.getForegroundPermissionsAsync();
      
      if (status !== 'granted') {
        console.log("Location permission not granted, requesting...");
        const { status: newStatus } = await Location.requestForegroundPermissionsAsync();
        
        if (newStatus !== 'granted') {
          console.error("Location permission denied by user");
          Alert.alert(
            "Location Required",
            "Shake & Match requires location access to find nearby matches. Without location, the app cannot function properly.",
            [
              { text: "OK" },
              { 
                text: "Open Settings", 
                onPress: () => {
                  if (Platform.OS === 'ios') {
                    Linking.openURL('app-settings:');
                  } else {
                    Linking.openSettings();
                  }
                } 
              }
            ]
          );
          return;
        }
      }
      
      // Immediately get current location and set it - this prevents the "not available" error
      try {
        console.log("Getting initial location...");
        const initialLocation = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.Balanced
        });
        
        const formattedLocation = {
          latitude: initialLocation.coords.latitude,
          longitude: initialLocation.coords.longitude,
        };
        
        console.log("Initial location obtained:", JSON.stringify(formattedLocation));
        setLocation(formattedLocation);
        
        // IMPORTANT: Store location in AsyncStorage so it's available immediately
        await AsyncStorage.setItem('lastKnownLocation', JSON.stringify(formattedLocation));
        
        // Update server with initial location
        if (socketRef.current && socketRef.current.connected && user) {
          socketRef.current.emit('updateLocation', {
            userId: user.id,
            location: formattedLocation
          });
        }
      } catch (initLocError) {
        console.error("Error getting initial location:", initLocError);
        // Try to recover by using last known location from storage
        try {
          const storedLocation = await AsyncStorage.getItem('lastKnownLocation');
          if (storedLocation) {
            const parsedLocation = JSON.parse(storedLocation);
            console.log("Using stored location:", JSON.stringify(parsedLocation));
            setLocation(parsedLocation);
          }
        } catch (storageError) {
          console.error("Error reading stored location:", storageError);
        }
      }
      
      // Then start watching for location updates
      console.log("Starting location watcher...");
      locationWatcher = await Location.watchPositionAsync(
        {
          accuracy: Location.Accuracy.High,
          distanceInterval: 10, // Update every 10 meters
          timeInterval: 5000,   // Or at least every 5 seconds
        },
        (location) => {
          const newLocation = {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
          };
          
          console.log("Location updated:", JSON.stringify(newLocation));
          setLocation(newLocation);
          
          // IMPORTANT: Store each location update to ensure we always have the latest
          AsyncStorage.setItem('lastKnownLocation', JSON.stringify(newLocation))
            .catch(err => console.error("Error saving location:", err));
          
          // Update server with new location
          if (socketRef.current && socketRef.current.connected && user) {
            socketRef.current.emit('updateLocation', {
              userId: user.id,
              location: newLocation
            });
          }
        }
      );
      
      console.log("Location watch setup complete");
      
    } catch (error) {
      console.error('Error setting up location:', error);
      Alert.alert(
        "Location Error",
        "There was a problem accessing your location. Please make sure location services are enabled and try again."
      );
    }
  };

  // Handle shake event with enhanced error handling
  const handleShake = () => {
    // Don't trigger if we're already searching or missing required data
    if (shakeDetected || !user || !user.id) {
      const reason = shakeDetected ? "already shaking" : "missing user ID";
      console.log(`Shake ignored - ${reason}`);
      return;
    }
    
    console.log("Shake handler triggered");
    
    // IMPORTANT: Always try to get the latest saved location first
    // This prevents the "Location not available" error
    const getLatestLocation = async () => {
      try {
        // Try to get from AsyncStorage first as it's faster
        const storedLocation = await AsyncStorage.getItem('lastKnownLocation');
        if (storedLocation) {
          const parsedLocation = JSON.parse(storedLocation);
          console.log("Retrieved stored location:", JSON.stringify(parsedLocation));
          
          // Set location state directly to avoid "not available" error
          setLocation(parsedLocation);
          
          // We have a location, proceed with shake using stored location
          return processShake(parsedLocation);
        }
      } catch (error) {
        console.log("No stored location available, trying GPS");
      }
      
      // If no stored location is available, continue with the original flow
      if (!location) {
        // Get fresh location from GPS
        try {
          console.log("Getting fresh location...");
          const currentLocation = await Location.getCurrentPositionAsync({
            accuracy: Location.Accuracy.Balanced
          });
          
          const formattedLocation = {
            latitude: currentLocation.coords.latitude,
            longitude: currentLocation.coords.longitude,
          };
          
          console.log("Retrieved current location:", JSON.stringify(formattedLocation));
          
          // Store for future use
          await AsyncStorage.setItem('lastKnownLocation', JSON.stringify(formattedLocation));
          setLocation(formattedLocation);
          
          // Process shake with fresh location
          return processShake(formattedLocation);
        } catch (locationError) {
          console.error("Error getting current location:", locationError);
          Alert.alert(
            "Location Required",
            "Please make sure location services are enabled with high accuracy.",
            [
              { text: "OK" },
              { 
                text: "Open Settings", 
                onPress: () => {
                  if (Platform.OS === 'ios') {
                    Linking.openURL('app-settings:');
                  } else {
                    Linking.openSettings();
                  }
                }
              }
            ]
          );
          setShakeDetected(false);
          return;
        }
      }
      
      // If we already have a location from state, use it
      return processShake(location);
    };
    
    // Start the location retrieval process
    getLatestLocation();
  };

  // Helper function to process shake with a valid location
  const processShake = (currentLocation) => {
    setShakeDetected(true);
    
    // Log user information for debugging
    console.log(`Shake initiated by user: ${user.username}, ID: ${user.id}`);
    
    // Check if socket is connected
    if (!socketRef.current || !socketRef.current.connected) {
      console.error("Socket not connected. Attempting to reconnect...");
      // Try to reconnect socket
      if (serverAddress) {
        socketRef.current = io(`http://${serverAddress}`);
        socketRef.current.on('connect', () => {
          console.log('Socket reconnected, registering user');
          socketRef.current.emit('register', {
            userId: user.id,
            username: user.username
          });
        });
      }
      
      Alert.alert(
        "Connection Issue",
        "There was a problem connecting to the server. Please try again."
      );
      
      setShakeDetected(false);
      return;
    }
    
    // Send shake event to server
    if (isLoggedIn && socketRef.current && socketRef.current.connected) {
      console.log("Sending shake event to server");
      socketRef.current.emit('shake', {
        userId: user.id,
        username: user.username,
        location: currentLocation,
        maxDistance,
        timestamp: new Date().toISOString(),
        blockedUsers,
        rematchEnabled: true,
        minAge: minAge,
        maxAge: maxAge
      });
      
      console.log(`Shake event sent with userID: ${user.id}, username: ${user.username}, location:`, JSON.stringify(currentLocation));
    } else {
      console.log("Cannot send shake event:", {
        isLoggedIn,
        socketConnected: socketRef.current && socketRef.current.connected,
        hasLocation: !!currentLocation
      });
    }
    
    // Reset shake detection after a delay
    setTimeout(() => {
      console.log("Resetting shake detection state");
      setShakeDetected(false);
    }, 2000); // 2 second cooldown
  };

  // Handle login with improved user ID handling
  const handleLogin = async (userData, server) => {
    console.log('=== APP.JS HANDLE LOGIN CALLED ===');
    console.log('userData received:', JSON.stringify(userData));
    console.log('server received:', server);
    
    // Add validation to ensure userData is properly structured
    if (!userData) {
      console.error('Error: userData is null or undefined');
      Alert.alert('Login Error', 'Invalid user data received. Please try again.');
      return;
    }
    
    if (typeof userData !== 'object') {
      console.error('Error: userData is not an object, type:', typeof userData);
      Alert.alert('Login Error', 'Received invalid user data format. Please try again.');
      return;
    }
    
    // Validate the user ID
    const userId = userData.id;
    if (!userId) {
      console.error('Error: No user ID in userData:', userData);
      Alert.alert('Login Error', 'User ID missing from login data. Please try again.');
      return;
    }
    
    console.log('User ID validation passed:', userId);
    
    // Create a clean user object with explicitly defined properties (keep token if provided)
    const user = {
      id: userId,
      username: userData.username || 'User',
      token: userData.token || null,
    };
    
    console.log('Setting user state to:', JSON.stringify(user));
    setUser(user);
    
    console.log('Setting isLoggedIn to true');
    setIsLoggedIn(true);
    
    console.log('Setting serverAddress to:', server);
    setServerAddress(server);
    
    try {
      // Define exactly what we're storing
      const storageData = JSON.stringify(user);
      console.log('Storing in AsyncStorage:', storageData);
      
      // Try to store item by item with error handling
      try {
        await AsyncStorage.setItem('user', storageData);
        console.log('AsyncStorage: user data stored successfully');
      } catch (userError) {
        console.error('AsyncStorage: Error storing user data:', userError);
      }
      
      try {
        await AsyncStorage.setItem('serverAddress', server);
        console.log('AsyncStorage: server address stored successfully');
      } catch (serverError) {
        console.error('AsyncStorage: Error storing server address:', serverError);
      }
      
      // Verify storage
      try {
        const storedUser = await AsyncStorage.getItem('user');
        console.log('Verification - stored user data:', storedUser);
      } catch (verifyError) {
        console.error('Error verifying stored user data:', verifyError);
      }
    } catch (error) {
      console.error('Overall error in storing login data:', error);
    }
    
    console.log('=== LOGIN PROCESS COMPLETED ===');
  };

  // Handle logout
  const handleLogout = async () => {
    if (socketRef.current) {
      socketRef.current.disconnect();
    }
    
    // Store server address temporarily
    const currentServer = serverAddress;
    
    // Clear state
    setUser(null);
    setIsLoggedIn(false);
    setMatches([]);
    setBlockedUsers([]);
    
    try {
      // Remove user data but keep server address
      await AsyncStorage.removeItem('user');
      
      // Make sure we restore the server address
      setServerAddress(currentServer);
    } catch (error) {
      console.error('Error during logout:', error);
    }
  };

  // Update max distance setting
  const updateMaxDistance = async (distance) => {
    setMaxDistance(distance);
    try {
      await AsyncStorage.setItem('maxDistance', distance.toString());
    } catch (error) {
      console.error('Error saving max distance:', error);
    }
  };

  return (
    <View style={{ flex: 1 }}>
      <StatusBar barStyle="dark-content" backgroundColor="#fff" />
      
      <NavigationContainer ref={navigationRef}>
  <Stack.Navigator 
    initialRouteName={isLoggedIn ? "Home" : "Login"}
    screenOptions={{
      headerShown: true,
      headerStyle: {
        backgroundColor: '#fff',
        elevation: 0,
        shadowOpacity: 0,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
      },
      headerTitleStyle: {
        fontWeight: '600',
        color: '#4e9af1',
      },
      headerTintColor: '#4e9af1',
      // Remove all animations - instant transitions
      cardStyleInterpolator: () => ({
        cardStyle: {
          opacity: 1,
        },
      }),
      // Set transition duration to 0 for instant transitions
      transitionSpec: {
        open: {
          animation: 'timing',
          config: {
            duration: 0,
          },
        },
        close: {
          animation: 'timing',
          config: {
            duration: 0,
          },
        },
      },
    }}
  >
    {!isLoggedIn ? (
      // Auth screens
      <>
        <Stack.Screen
          name="Login"
          options={{
            title: "Welcome",
            headerShown: false
          }}
        >
          {props => (
            <LoginScreen
              {...props}
              onLogin={handleLogin}
              serverAddress={serverAddress}
            />
          )}
        </Stack.Screen>
      </>
    ) : (
      <>
        <Stack.Screen
          name="Home"
          options={{
            title: "Shake & Match",
            headerBackTitle: "Shake"
          }}
        >
          {props => (
            <HomeScreen
              {...props}
              user={user}
              userProfile={userProfile}
              shakeDetected={shakeDetected}
              matches={matches}
              onShake={handleShake}
              onDeleteMatch={handleDeleteChat}
              onUnmatch={handleUnmatch}
              onBlockUser={handleBlockUser}
              serverAddress={serverAddress}
            />
          )}
        </Stack.Screen>
        
        {/* All other screens remain the same */}
        <Stack.Screen
          name="Settings"
          options={{
            title: "Settings",
            headerBackTitle: "Shake",
            headerBackTitleVisible: true,
          }}
        >
          {props => <SettingsScreen {...props} maxDistance={maxDistance} minAge={minAge} maxAge={maxAge} blockedUsers={blockedUsers} onUpdateMaxDistance={updateMaxDistance} onUpdateAgeRange={updateAgeRange} onUnblockUser={handleUnblockUser} onLogout={handleLogout} user={user} serverAddress={serverAddress} />}
        </Stack.Screen>
        <Stack.Screen name="Chat">
          {props => <ChatScreen {...props} socket={socketRef.current} user={user} onDeleteChat={handleDeleteChat} onBlockUser={handleBlockUser} />}
        </Stack.Screen>
        <Stack.Screen name="Match">
          {props => <MatchScreen {...props} serverAddress={serverAddress} />}
        </Stack.Screen>
        <Stack.Screen
          name="ViewProfile"
          component={ViewProfileScreen}
          options={{
            headerBackTitle: "Conversation",
            headerBackTitleVisible: true,
          }}
        />
        <Stack.Screen
          name="ProfileScreen"
          options={{
            title: "Profile",
            headerBackTitle: "Shake",
            headerBackTitleVisible: true,
          }}
        >
          {props => (
            <ProfileScreen
              {...props}
              user={user}
              serverAddress={serverAddress}
              onUpdateProfile={handleUpdateProfile}
              isInitialSetup={props.route.params?.isInitialSetup || false}
            />
          )}
        </Stack.Screen>
      </>
    )}
  </Stack.Navigator>
</NavigationContainer>
    </View>
  );
}